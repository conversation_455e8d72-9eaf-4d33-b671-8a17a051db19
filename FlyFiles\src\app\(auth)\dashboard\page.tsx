"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Progress } from "@/app/components/ui/progress"
import { Upload, File, Download, Calendar, Settings, TrendingUp } from "lucide-react"
import { formatFileSize, formatDate, calculateUsagePercentage, getPlanDisplayName } from "@/app/lib/utils"
import { PLAN_CONFIGS } from "@/app/lib/mongodb"
import { FileRecord, UsageStats } from "@/app/lib/types"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [files, setFiles] = useState<FileRecord[]>([])
  const [usage, setUsage] = useState<UsageStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === "loading") return
    
    if (!session) {
      router.push('/login')
      return
    }

    // Load user data
    loadDashboardData()
  }, [session, status, router])

  const loadDashboardData = async () => {
    try {
      // Load usage stats
      const usageResponse = await fetch('/api/user/usage')
      if (usageResponse.ok) {
        const usageData = await usageResponse.json()
        setUsage(usageData.data)
      }

      // Load files
      const filesResponse = await fetch('/api/files')
      if (filesResponse.ok) {
        const filesData = await filesResponse.json()
        setFiles(filesData.data || [])
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Indlæser dashboard...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null // Will redirect to login
  }

  const userPlan = (session.user?.plan as any) || 'free'
  const planConfig = PLAN_CONFIGS[userPlan as keyof typeof PLAN_CONFIGS]
  const usagePercentage = usage ? calculateUsagePercentage(usage.currentUsage, planConfig.uploadLimit.amount) : 0

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/home-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
              Velkommen tilbage
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-extrabold mb-4 sm:mb-6 leading-tight">
              Hej <span className="text-blue-300">{session.user?.name}</span>
            </h1>
            <p className="text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 max-w-2xl mx-auto">
              Administrer dine filer og se din forbrugsstatistik
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">

        {/* Usage Overview */}
        {usage && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Upload forbrug</span>
                </CardTitle>
                <CardDescription>
                  Din nuværende {usage.period} forbrug
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold">
                      {formatFileSize(usage.current)} / {formatFileSize(usage.limit)}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {usage.percentage.toFixed(1)}% brugt
                    </span>
                  </div>
                  <Progress value={usage.percentage} className="h-3" />
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Plan: {getPlanDisplayName(usage.plan)}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Plan status</CardTitle>
                <CardDescription>
                  Din nuværende plan og fordele
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="font-semibold">{planConfig.name}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    • {formatFileSize(planConfig.uploadLimit.amount)} per {
                      planConfig.uploadLimit.period === 'week' ? 'uge' : 'måned'
                    }
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    • {planConfig.fileExpiry} dages opbevaring
                  </div>
                  {'price' in planConfig && (
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      • {planConfig.price.amount} kr/måned
                    </div>
                  )}
                  <Button variant="outline" size="sm" className="mt-4 w-full">
                    Skift plan
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Upload Area */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload nye filer</span>
            </CardTitle>
            <CardDescription>
              Drag og slip filer her eller klik for at vælge filer
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-8 text-center hover:border-blue-500 transition-colors cursor-pointer">
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Vælg filer til upload</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Maksimal filstørrelse: {formatFileSize(planConfig.uploadLimit.amount)}
              </p>
              <Button>Vælg filer</Button>
            </div>
          </CardContent>
        </Card>

        {/* Files Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <File className="h-5 w-5" />
              <span>Mine filer</span>
            </CardTitle>
            <CardDescription>
              Administrer dine uploadede filer og download-grænser
            </CardDescription>
          </CardHeader>
          <CardContent>
            {files.length === 0 ? (
              <div className="text-center py-8">
                <File className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Ingen filer endnu</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Upload din første fil for at se den her
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-700">
                      <th className="text-left py-3 px-4">Filnavn</th>
                      <th className="text-left py-3 px-4">Størrelse</th>
                      <th className="text-left py-3 px-4">Upload dato</th>
                      <th className="text-left py-3 px-4">Udløber</th>
                      <th className="text-left py-3 px-4">Downloads</th>
                      <th className="text-left py-3 px-4">Grænse</th>
                      <th className="text-right py-3 px-4">Handlinger</th>
                    </tr>
                  </thead>
                  <tbody>
                    {files.map((file) => (
                      <tr key={file._id} className="border-b border-gray-100 dark:border-gray-800">
                        <td className="py-3 px-4">
                          <div className="font-medium">{file.originalName}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">{file.mimeType}</div>
                        </td>
                        <td className="py-3 px-4">{formatFileSize(file.size)}</td>
                        <td className="py-3 px-4">{formatDate(file.uploadDate)}</td>
                        <td className="py-3 px-4">{formatDate(file.expiryDate)}</td>
                        <td className="py-3 px-4">{file.downloadCount}</td>
                        <td className="py-3 px-4">
                          {file.downloadLimit === -1 ? 'Ubegrænset' : file.downloadLimit}
                        </td>
                        <td className="py-3 px-4 text-right">
                          <Button variant="ghost" size="sm">
                            <Settings className="h-4 w-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 