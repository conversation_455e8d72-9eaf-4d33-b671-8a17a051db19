"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jose";
exports.ids = ["vendor-chunks/jose"];
exports.modules = {

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/decrypt.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwe/compact/decrypt.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compactDecrypt: () => (/* binding */ compactDecrypt)\n/* harmony export */ });\n/* harmony import */ var _flattened_decrypt_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../flattened/decrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\n\nasync function compactDecrypt(jwe, key, options) {\n    if (jwe instanceof Uint8Array) {\n        jwe = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(jwe);\n    }\n    if (typeof jwe !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Compact JWE must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: encryptedKey, 2: iv, 3: ciphertext, 4: tag, length, } = jwe.split('.');\n    if (length !== 5) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Invalid Compact JWE');\n    }\n    const decrypted = await (0,_flattened_decrypt_js__WEBPACK_IMPORTED_MODULE_2__.flattenedDecrypt)({\n        ciphertext,\n        iv: iv || undefined,\n        protected: protectedHeader,\n        tag: tag || undefined,\n        encrypted_key: encryptedKey || undefined,\n    }, key, options);\n    const result = { plaintext: decrypted.plaintext, protectedHeader: decrypted.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/encrypt.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwe/compact/encrypt.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactEncrypt: () => (/* binding */ CompactEncrypt)\n/* harmony export */ });\n/* harmony import */ var _flattened_encrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../flattened/encrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js\");\n\nclass CompactEncrypt {\n    _flattened;\n    constructor(plaintext) {\n        this._flattened = new _flattened_encrypt_js__WEBPACK_IMPORTED_MODULE_0__.FlattenedEncrypt(plaintext);\n    }\n    setContentEncryptionKey(cek) {\n        this._flattened.setContentEncryptionKey(cek);\n        return this;\n    }\n    setInitializationVector(iv) {\n        this._flattened.setInitializationVector(iv);\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        this._flattened.setKeyManagementParameters(parameters);\n        return this;\n    }\n    async encrypt(key, options) {\n        const jwe = await this._flattened.encrypt(key, options);\n        return [jwe.protected, jwe.encrypted_key, jwe.iv, jwe.ciphertext, jwe.tag].join('.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2p3ZS9jb21wYWN0L2VuY3J5cHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkQ7QUFDcEQ7QUFDUDtBQUNBO0FBQ0EsOEJBQThCLG1FQUFnQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGp3ZVxcY29tcGFjdFxcZW5jcnlwdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBGbGF0dGVuZWRFbmNyeXB0IH0gZnJvbSAnLi4vZmxhdHRlbmVkL2VuY3J5cHQuanMnO1xuZXhwb3J0IGNsYXNzIENvbXBhY3RFbmNyeXB0IHtcbiAgICBfZmxhdHRlbmVkO1xuICAgIGNvbnN0cnVjdG9yKHBsYWludGV4dCkge1xuICAgICAgICB0aGlzLl9mbGF0dGVuZWQgPSBuZXcgRmxhdHRlbmVkRW5jcnlwdChwbGFpbnRleHQpO1xuICAgIH1cbiAgICBzZXRDb250ZW50RW5jcnlwdGlvbktleShjZWspIHtcbiAgICAgICAgdGhpcy5fZmxhdHRlbmVkLnNldENvbnRlbnRFbmNyeXB0aW9uS2V5KGNlayk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBzZXRJbml0aWFsaXphdGlvblZlY3Rvcihpdikge1xuICAgICAgICB0aGlzLl9mbGF0dGVuZWQuc2V0SW5pdGlhbGl6YXRpb25WZWN0b3IoaXYpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgc2V0UHJvdGVjdGVkSGVhZGVyKHByb3RlY3RlZEhlYWRlcikge1xuICAgICAgICB0aGlzLl9mbGF0dGVuZWQuc2V0UHJvdGVjdGVkSGVhZGVyKHByb3RlY3RlZEhlYWRlcik7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBzZXRLZXlNYW5hZ2VtZW50UGFyYW1ldGVycyhwYXJhbWV0ZXJzKSB7XG4gICAgICAgIHRoaXMuX2ZsYXR0ZW5lZC5zZXRLZXlNYW5hZ2VtZW50UGFyYW1ldGVycyhwYXJhbWV0ZXJzKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIGFzeW5jIGVuY3J5cHQoa2V5LCBvcHRpb25zKSB7XG4gICAgICAgIGNvbnN0IGp3ZSA9IGF3YWl0IHRoaXMuX2ZsYXR0ZW5lZC5lbmNyeXB0KGtleSwgb3B0aW9ucyk7XG4gICAgICAgIHJldHVybiBbandlLnByb3RlY3RlZCwgandlLmVuY3J5cHRlZF9rZXksIGp3ZS5pdiwgandlLmNpcGhlcnRleHQsIGp3ZS50YWddLmpvaW4oJy4nKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenedDecrypt: () => (/* binding */ flattenedDecrypt)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../runtime/decrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/decrypt.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _lib_decrypt_key_management_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/decrypt_key_management.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/decrypt_key_management.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../lib/cek.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n/* harmony import */ var _lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/validate_algorithms.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/validate_algorithms.js\");\n\n\n\n\n\n\n\n\n\n\nasync function flattenedDecrypt(jwe, key, options) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Flattened JWE must be an object');\n    }\n    if (jwe.protected === undefined && jwe.header === undefined && jwe.unprotected === undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JOSE Header missing');\n    }\n    if (jwe.iv !== undefined && typeof jwe.iv !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Initialization Vector incorrect type');\n    }\n    if (typeof jwe.ciphertext !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Ciphertext missing or incorrect type');\n    }\n    if (jwe.tag !== undefined && typeof jwe.tag !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Authentication Tag incorrect type');\n    }\n    if (jwe.protected !== undefined && typeof jwe.protected !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected Header incorrect type');\n    }\n    if (jwe.encrypted_key !== undefined && typeof jwe.encrypted_key !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Encrypted Key incorrect type');\n    }\n    if (jwe.aad !== undefined && typeof jwe.aad !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE AAD incorrect type');\n    }\n    if (jwe.header !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe.header)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Shared Unprotected Header incorrect type');\n    }\n    if (jwe.unprotected !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe.unprotected)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Per-Recipient Unprotected Header incorrect type');\n    }\n    let parsedProt;\n    if (jwe.protected) {\n        try {\n            const protectedHeader = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.protected);\n            parsedProt = JSON.parse(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected Header is invalid');\n        }\n    }\n    if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(parsedProt, jwe.header, jwe.unprotected)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jwe.header,\n        ...jwe.unprotected,\n    };\n    (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid, new Map(), options?.crit, parsedProt, joseHeader);\n    if (joseHeader.zip !== undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n    }\n    const { alg, enc } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('missing JWE Algorithm (alg) in JWE Header');\n    }\n    if (typeof enc !== 'string' || !enc) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('missing JWE Encryption Algorithm (enc) in JWE Header');\n    }\n    const keyManagementAlgorithms = options && (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('keyManagementAlgorithms', options.keyManagementAlgorithms);\n    const contentEncryptionAlgorithms = options &&\n        (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('contentEncryptionAlgorithms', options.contentEncryptionAlgorithms);\n    if ((keyManagementAlgorithms && !keyManagementAlgorithms.has(alg)) ||\n        (!keyManagementAlgorithms && alg.startsWith('PBES2'))) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (contentEncryptionAlgorithms && !contentEncryptionAlgorithms.has(enc)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"enc\" (Encryption Algorithm) Header Parameter value not allowed');\n    }\n    let encryptedKey;\n    if (jwe.encrypted_key !== undefined) {\n        try {\n            encryptedKey = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.encrypted_key);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the encrypted_key');\n        }\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jwe);\n        resolvedKey = true;\n    }\n    let cek;\n    try {\n        cek = await (0,_lib_decrypt_key_management_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(alg, key, encryptedKey, joseHeader, options);\n    }\n    catch (err) {\n        if (err instanceof TypeError || err instanceof _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid || err instanceof _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported) {\n            throw err;\n        }\n        cek = (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(enc);\n    }\n    let iv;\n    let tag;\n    if (jwe.iv !== undefined) {\n        try {\n            iv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.iv);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the iv');\n        }\n    }\n    if (jwe.tag !== undefined) {\n        try {\n            tag = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.tag);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the tag');\n        }\n    }\n    const protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jwe.protected ?? '');\n    let additionalData;\n    if (jwe.aad !== undefined) {\n        additionalData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode('.'), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jwe.aad));\n    }\n    else {\n        additionalData = protectedHeader;\n    }\n    let ciphertext;\n    try {\n        ciphertext = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.ciphertext);\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the ciphertext');\n    }\n    const plaintext = await (0,_runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(enc, cek, ciphertext, iv, tag, additionalData);\n    const result = { plaintext };\n    if (jwe.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jwe.aad !== undefined) {\n        try {\n            result.additionalAuthenticatedData = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.aad);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the aad');\n        }\n    }\n    if (jwe.unprotected !== undefined) {\n        result.sharedUnprotectedHeader = jwe.unprotected;\n    }\n    if (jwe.header !== undefined) {\n        result.unprotectedHeader = jwe.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlattenedEncrypt: () => (/* binding */ FlattenedEncrypt)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _lib_private_symbols_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/private_symbols.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/private_symbols.js\");\n/* harmony import */ var _runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../runtime/encrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/encrypt.js\");\n/* harmony import */ var _lib_encrypt_key_management_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/encrypt_key_management.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/encrypt_key_management.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n\n\n\n\n\n\n\n\nclass FlattenedEncrypt {\n    _plaintext;\n    _protectedHeader;\n    _sharedUnprotectedHeader;\n    _unprotectedHeader;\n    _aad;\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    constructor(plaintext) {\n        if (!(plaintext instanceof Uint8Array)) {\n            throw new TypeError('plaintext must be an instance of Uint8Array');\n        }\n        this._plaintext = plaintext;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this._sharedUnprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this._sharedUnprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this._aad = aad;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    async encrypt(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader && !this._sharedUnprotectedHeader) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()');\n        }\n        if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._protectedHeader, this._unprotectedHeader, this._sharedUnprotectedHeader)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n            ...this._sharedUnprotectedHeader,\n        };\n        (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid, new Map(), options?.crit, this._protectedHeader, joseHeader);\n        if (joseHeader.zip !== undefined) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n        }\n        const { alg, enc } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        if (typeof enc !== 'string' || !enc) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n        }\n        let encryptedKey;\n        if (this._cek && (alg === 'dir' || alg === 'ECDH-ES')) {\n            throw new TypeError(`setContentEncryptionKey cannot be called with JWE \"alg\" (Algorithm) Header ${alg}`);\n        }\n        let cek;\n        {\n            let parameters;\n            ({ cek, encryptedKey, parameters } = await (0,_lib_encrypt_key_management_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(alg, enc, key, this._cek, this._keyManagementParameters));\n            if (parameters) {\n                if (options && _lib_private_symbols_js__WEBPACK_IMPORTED_MODULE_4__.unprotected in options) {\n                    if (!this._unprotectedHeader) {\n                        this.setUnprotectedHeader(parameters);\n                    }\n                    else {\n                        this._unprotectedHeader = { ...this._unprotectedHeader, ...parameters };\n                    }\n                }\n                else if (!this._protectedHeader) {\n                    this.setProtectedHeader(parameters);\n                }\n                else {\n                    this._protectedHeader = { ...this._protectedHeader, ...parameters };\n                }\n            }\n        }\n        let additionalData;\n        let protectedHeader;\n        let aadMember;\n        if (this._protectedHeader) {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode((0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode('');\n        }\n        if (this._aad) {\n            aadMember = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(this._aad);\n            additionalData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode('.'), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode(aadMember));\n        }\n        else {\n            additionalData = protectedHeader;\n        }\n        const { ciphertext, tag, iv } = await (0,_runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(enc, this._plaintext, cek, this._iv, additionalData);\n        const jwe = {\n            ciphertext: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(ciphertext),\n        };\n        if (iv) {\n            jwe.iv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(iv);\n        }\n        if (tag) {\n            jwe.tag = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(tag);\n        }\n        if (encryptedKey) {\n            jwe.encrypted_key = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(encryptedKey);\n        }\n        if (aadMember) {\n            jwe.aad = aadMember;\n        }\n        if (this._protectedHeader) {\n            jwe.protected = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.decoder.decode(protectedHeader);\n        }\n        if (this._sharedUnprotectedHeader) {\n            jwe.unprotected = this._sharedUnprotectedHeader;\n        }\n        if (this._unprotectedHeader) {\n            jwe.header = this._unprotectedHeader;\n        }\n        return jwe;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwk/thumbprint.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwk/thumbprint.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateJwkThumbprint: () => (/* binding */ calculateJwkThumbprint),\n/* harmony export */   calculateJwkThumbprintUri: () => (/* binding */ calculateJwkThumbprintUri)\n/* harmony export */ });\n/* harmony import */ var _runtime_digest_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../runtime/digest.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n\n\n\n\n\nconst check = (value, description) => {\n    if (typeof value !== 'string' || !value) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWKInvalid(`${description} missing or invalid`);\n    }\n};\nasync function calculateJwkThumbprint(jwk, digestAlgorithm) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    digestAlgorithm ??= 'sha256';\n    if (digestAlgorithm !== 'sha256' &&\n        digestAlgorithm !== 'sha384' &&\n        digestAlgorithm !== 'sha512') {\n        throw new TypeError('digestAlgorithm must one of \"sha256\", \"sha384\", or \"sha512\"');\n    }\n    let components;\n    switch (jwk.kty) {\n        case 'EC':\n            check(jwk.crv, '\"crv\" (Curve) Parameter');\n            check(jwk.x, '\"x\" (X Coordinate) Parameter');\n            check(jwk.y, '\"y\" (Y Coordinate) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x, y: jwk.y };\n            break;\n        case 'OKP':\n            check(jwk.crv, '\"crv\" (Subtype of Key Pair) Parameter');\n            check(jwk.x, '\"x\" (Public Key) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x };\n            break;\n        case 'RSA':\n            check(jwk.e, '\"e\" (Exponent) Parameter');\n            check(jwk.n, '\"n\" (Modulus) Parameter');\n            components = { e: jwk.e, kty: jwk.kty, n: jwk.n };\n            break;\n        case 'oct':\n            check(jwk.k, '\"k\" (Key Value) Parameter');\n            components = { k: jwk.k, kty: jwk.kty };\n            break;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('\"kty\" (Key Type) Parameter missing or unsupported');\n    }\n    const data = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__.encoder.encode(JSON.stringify(components));\n    return (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_3__.encode)(await (0,_runtime_digest_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(digestAlgorithm, data));\n}\nasync function calculateJwkThumbprintUri(jwk, digestAlgorithm) {\n    digestAlgorithm ??= 'sha256';\n    const thumbprint = await calculateJwkThumbprint(jwk, digestAlgorithm);\n    return `urn:ietf:params:oauth:jwk-thumbprint:sha-${digestAlgorithm.slice(-3)}:${thumbprint}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwk/thumbprint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwt/decrypt.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwt/decrypt.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jwtDecrypt: () => (/* binding */ jwtDecrypt)\n/* harmony export */ });\n/* harmony import */ var _jwe_compact_decrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jwe/compact/decrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/decrypt.js\");\n/* harmony import */ var _lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/jwt_claims_set.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/jwt_claims_set.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\n\n\nasync function jwtDecrypt(jwt, key, options) {\n    const decrypted = await (0,_jwe_compact_decrypt_js__WEBPACK_IMPORTED_MODULE_0__.compactDecrypt)(jwt, key, options);\n    const payload = (0,_lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(decrypted.protectedHeader, decrypted.plaintext, options);\n    const { protectedHeader } = decrypted;\n    if (protectedHeader.iss !== undefined && protectedHeader.iss !== payload.iss) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"iss\" claim header parameter mismatch', payload, 'iss', 'mismatch');\n    }\n    if (protectedHeader.sub !== undefined && protectedHeader.sub !== payload.sub) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"sub\" claim header parameter mismatch', payload, 'sub', 'mismatch');\n    }\n    if (protectedHeader.aud !== undefined &&\n        JSON.stringify(protectedHeader.aud) !== JSON.stringify(payload.aud)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"aud\" claim header parameter mismatch', payload, 'aud', 'mismatch');\n    }\n    const result = { payload, protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwt/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwt/encrypt.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwt/encrypt.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EncryptJWT: () => (/* binding */ EncryptJWT)\n/* harmony export */ });\n/* harmony import */ var _jwe_compact_encrypt_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jwe/compact/encrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwe/compact/encrypt.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _produce_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./produce.js */ \"(rsc)/./node_modules/jose/dist/node/esm/jwt/produce.js\");\n\n\n\nclass EncryptJWT extends _produce_js__WEBPACK_IMPORTED_MODULE_0__.ProduceJWT {\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    _protectedHeader;\n    _replicateIssuerAsHeader;\n    _replicateSubjectAsHeader;\n    _replicateAudienceAsHeader;\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    replicateIssuerAsHeader() {\n        this._replicateIssuerAsHeader = true;\n        return this;\n    }\n    replicateSubjectAsHeader() {\n        this._replicateSubjectAsHeader = true;\n        return this;\n    }\n    replicateAudienceAsHeader() {\n        this._replicateAudienceAsHeader = true;\n        return this;\n    }\n    async encrypt(key, options) {\n        const enc = new _jwe_compact_encrypt_js__WEBPACK_IMPORTED_MODULE_1__.CompactEncrypt(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__.encoder.encode(JSON.stringify(this._payload)));\n        if (this._replicateIssuerAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, iss: this._payload.iss };\n        }\n        if (this._replicateSubjectAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, sub: this._payload.sub };\n        }\n        if (this._replicateAudienceAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, aud: this._payload.aud };\n        }\n        enc.setProtectedHeader(this._protectedHeader);\n        if (this._iv) {\n            enc.setInitializationVector(this._iv);\n        }\n        if (this._cek) {\n            enc.setContentEncryptionKey(this._cek);\n        }\n        if (this._keyManagementParameters) {\n            enc.setKeyManagementParameters(this._keyManagementParameters);\n        }\n        return enc.encrypt(key, options);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwt/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/jwt/produce.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/jwt/produce.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProduceJWT: () => (/* binding */ ProduceJWT)\n/* harmony export */ });\n/* harmony import */ var _lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/epoch.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/epoch.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _lib_secs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/secs.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/secs.js\");\n\n\n\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nclass ProduceJWT {\n    _payload;\n    constructor(payload = {}) {\n        if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this._payload = payload;\n    }\n    setIssuer(issuer) {\n        this._payload = { ...this._payload, iss: issuer };\n        return this;\n    }\n    setSubject(subject) {\n        this._payload = { ...this._payload, sub: subject };\n        return this;\n    }\n    setAudience(audience) {\n        this._payload = { ...this._payload, aud: audience };\n        return this;\n    }\n    setJti(jwtId) {\n        this._payload = { ...this._payload, jti: jwtId };\n        return this;\n    }\n    setNotBefore(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, nbf: (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) + (0,_lib_secs_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input) };\n        }\n        return this;\n    }\n    setExpirationTime(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, exp: (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) + (0,_lib_secs_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input) };\n        }\n        return this;\n    }\n    setIssuedAt(input) {\n        if (typeof input === 'undefined') {\n            this._payload = { ...this._payload, iat: (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input)) };\n        }\n        else if (typeof input === 'string') {\n            this._payload = {\n                ...this._payload,\n                iat: validateInput('setIssuedAt', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) + (0,_lib_secs_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input)),\n            };\n        }\n        else {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', input) };\n        }\n        return this;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2p3dC9wcm9kdWNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0M7QUFDTztBQUNUO0FBQ2xDO0FBQ0E7QUFDQSx1Q0FBdUMsT0FBTztBQUM5QztBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsNEJBQTRCO0FBQzVCLGFBQWEsNkRBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0EsOEJBQThCLHFEQUFxRCx5REFBSztBQUN4RjtBQUNBO0FBQ0EsOEJBQThCLHVCQUF1Qix5REFBSyxlQUFlLHdEQUFJO0FBQzdFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBLDhCQUE4QiwwREFBMEQseURBQUs7QUFDN0Y7QUFDQTtBQUNBLDhCQUE4Qix1QkFBdUIseURBQUssZUFBZSx3REFBSTtBQUM3RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHVCQUF1Qix5REFBSztBQUMxRDtBQUNBO0FBQ0EsOEJBQThCLG9EQUFvRCx5REFBSztBQUN2RjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCx5REFBSyxlQUFlLHdEQUFJO0FBQzFFO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGp3dFxccHJvZHVjZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZXBvY2ggZnJvbSAnLi4vbGliL2Vwb2NoLmpzJztcbmltcG9ydCBpc09iamVjdCBmcm9tICcuLi9saWIvaXNfb2JqZWN0LmpzJztcbmltcG9ydCBzZWNzIGZyb20gJy4uL2xpYi9zZWNzLmpzJztcbmZ1bmN0aW9uIHZhbGlkYXRlSW5wdXQobGFiZWwsIGlucHV0KSB7XG4gICAgaWYgKCFOdW1iZXIuaXNGaW5pdGUoaW5wdXQpKSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYEludmFsaWQgJHtsYWJlbH0gaW5wdXRgKTtcbiAgICB9XG4gICAgcmV0dXJuIGlucHV0O1xufVxuZXhwb3J0IGNsYXNzIFByb2R1Y2VKV1Qge1xuICAgIF9wYXlsb2FkO1xuICAgIGNvbnN0cnVjdG9yKHBheWxvYWQgPSB7fSkge1xuICAgICAgICBpZiAoIWlzT2JqZWN0KHBheWxvYWQpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdKV1QgQ2xhaW1zIFNldCBNVVNUIGJlIGFuIG9iamVjdCcpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuX3BheWxvYWQgPSBwYXlsb2FkO1xuICAgIH1cbiAgICBzZXRJc3N1ZXIoaXNzdWVyKSB7XG4gICAgICAgIHRoaXMuX3BheWxvYWQgPSB7IC4uLnRoaXMuX3BheWxvYWQsIGlzczogaXNzdWVyIH07XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBzZXRTdWJqZWN0KHN1YmplY3QpIHtcbiAgICAgICAgdGhpcy5fcGF5bG9hZCA9IHsgLi4udGhpcy5fcGF5bG9hZCwgc3ViOiBzdWJqZWN0IH07XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBzZXRBdWRpZW5jZShhdWRpZW5jZSkge1xuICAgICAgICB0aGlzLl9wYXlsb2FkID0geyAuLi50aGlzLl9wYXlsb2FkLCBhdWQ6IGF1ZGllbmNlIH07XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBzZXRKdGkoand0SWQpIHtcbiAgICAgICAgdGhpcy5fcGF5bG9hZCA9IHsgLi4udGhpcy5fcGF5bG9hZCwganRpOiBqd3RJZCB9O1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgc2V0Tm90QmVmb3JlKGlucHV0KSB7XG4gICAgICAgIGlmICh0eXBlb2YgaW5wdXQgPT09ICdudW1iZXInKSB7XG4gICAgICAgICAgICB0aGlzLl9wYXlsb2FkID0geyAuLi50aGlzLl9wYXlsb2FkLCBuYmY6IHZhbGlkYXRlSW5wdXQoJ3NldE5vdEJlZm9yZScsIGlucHV0KSB9O1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGlucHV0IGluc3RhbmNlb2YgRGF0ZSkge1xuICAgICAgICAgICAgdGhpcy5fcGF5bG9hZCA9IHsgLi4udGhpcy5fcGF5bG9hZCwgbmJmOiB2YWxpZGF0ZUlucHV0KCdzZXROb3RCZWZvcmUnLCBlcG9jaChpbnB1dCkpIH07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLl9wYXlsb2FkID0geyAuLi50aGlzLl9wYXlsb2FkLCBuYmY6IGVwb2NoKG5ldyBEYXRlKCkpICsgc2VjcyhpbnB1dCkgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgc2V0RXhwaXJhdGlvblRpbWUoaW5wdXQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBpbnB1dCA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgIHRoaXMuX3BheWxvYWQgPSB7IC4uLnRoaXMuX3BheWxvYWQsIGV4cDogdmFsaWRhdGVJbnB1dCgnc2V0RXhwaXJhdGlvblRpbWUnLCBpbnB1dCkgfTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpbnB1dCBpbnN0YW5jZW9mIERhdGUpIHtcbiAgICAgICAgICAgIHRoaXMuX3BheWxvYWQgPSB7IC4uLnRoaXMuX3BheWxvYWQsIGV4cDogdmFsaWRhdGVJbnB1dCgnc2V0RXhwaXJhdGlvblRpbWUnLCBlcG9jaChpbnB1dCkpIH07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLl9wYXlsb2FkID0geyAuLi50aGlzLl9wYXlsb2FkLCBleHA6IGVwb2NoKG5ldyBEYXRlKCkpICsgc2VjcyhpbnB1dCkgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgc2V0SXNzdWVkQXQoaW5wdXQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBpbnB1dCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRoaXMuX3BheWxvYWQgPSB7IC4uLnRoaXMuX3BheWxvYWQsIGlhdDogZXBvY2gobmV3IERhdGUoKSkgfTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpbnB1dCBpbnN0YW5jZW9mIERhdGUpIHtcbiAgICAgICAgICAgIHRoaXMuX3BheWxvYWQgPSB7IC4uLnRoaXMuX3BheWxvYWQsIGlhdDogdmFsaWRhdGVJbnB1dCgnc2V0SXNzdWVkQXQnLCBlcG9jaChpbnB1dCkpIH07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodHlwZW9mIGlucHV0ID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgdGhpcy5fcGF5bG9hZCA9IHtcbiAgICAgICAgICAgICAgICAuLi50aGlzLl9wYXlsb2FkLFxuICAgICAgICAgICAgICAgIGlhdDogdmFsaWRhdGVJbnB1dCgnc2V0SXNzdWVkQXQnLCBlcG9jaChuZXcgRGF0ZSgpKSArIHNlY3MoaW5wdXQpKSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLl9wYXlsb2FkID0geyAuLi50aGlzLl9wYXlsb2FkLCBpYXQ6IHZhbGlkYXRlSW5wdXQoJ3NldElzc3VlZEF0JywgaW5wdXQpIH07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/jwt/produce.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/key/export.js":
/*!*******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/key/export.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exportJWK: () => (/* binding */ exportJWK),\n/* harmony export */   exportPKCS8: () => (/* binding */ exportPKCS8),\n/* harmony export */   exportSPKI: () => (/* binding */ exportSPKI)\n/* harmony export */ });\n/* harmony import */ var _runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/asn1.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/asn1.js\");\n/* harmony import */ var _runtime_key_to_jwk_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/key_to_jwk.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/key_to_jwk.js\");\n\n\n\nasync function exportSPKI(key) {\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.toSPKI)(key);\n}\nasync function exportPKCS8(key) {\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.toPKCS8)(key);\n}\nasync function exportJWK(key) {\n    return (0,_runtime_key_to_jwk_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2tleS9leHBvcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEQ7QUFDRTtBQUNkO0FBQ3pDO0FBQ1AsV0FBVyx3REFBWTtBQUN2QjtBQUNPO0FBQ1AsV0FBVyx5REFBYTtBQUN4QjtBQUNPO0FBQ1AsV0FBVyxrRUFBUTtBQUNuQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGtleVxcZXhwb3J0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHRvU1BLSSBhcyBleHBvcnRQdWJsaWMgfSBmcm9tICcuLi9ydW50aW1lL2FzbjEuanMnO1xuaW1wb3J0IHsgdG9QS0NTOCBhcyBleHBvcnRQcml2YXRlIH0gZnJvbSAnLi4vcnVudGltZS9hc24xLmpzJztcbmltcG9ydCBrZXlUb0pXSyBmcm9tICcuLi9ydW50aW1lL2tleV90b19qd2suanMnO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGV4cG9ydFNQS0koa2V5KSB7XG4gICAgcmV0dXJuIGV4cG9ydFB1YmxpYyhrZXkpO1xufVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGV4cG9ydFBLQ1M4KGtleSkge1xuICAgIHJldHVybiBleHBvcnRQcml2YXRlKGtleSk7XG59XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZXhwb3J0SldLKGtleSkge1xuICAgIHJldHVybiBrZXlUb0pXSyhrZXkpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/key/export.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/key/import.js":
/*!*******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/key/import.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   importJWK: () => (/* binding */ importJWK),\n/* harmony export */   importPKCS8: () => (/* binding */ importPKCS8),\n/* harmony export */   importSPKI: () => (/* binding */ importSPKI),\n/* harmony export */   importX509: () => (/* binding */ importX509)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/asn1.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/asn1.js\");\n/* harmony import */ var _runtime_jwk_to_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../runtime/jwk_to_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/jwk_to_key.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n\n\n\n\n\nasync function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromSPKI)(spki, alg, options);\n}\nasync function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromX509)(x509, alg, options);\n}\nasync function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromPKCS8)(pkcs8, alg, options);\n}\nasync function importJWK(jwk, alg) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    alg ||= jwk.alg;\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            return (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwk.k);\n        case 'RSA':\n            if ('oth' in jwk && jwk.oth !== undefined) {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n        case 'EC':\n        case 'OKP':\n            return (0,_runtime_jwk_to_key_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({ ...jwk, alg });\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/key/import.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/aesgcmkw.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/aesgcmkw.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/encrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/encrypt.js\");\n/* harmony import */ var _runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../runtime/decrypt.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/decrypt.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n\n\n\nasync function wrap(alg, key, cek, iv) {\n    const jweAlgorithm = alg.slice(0, 7);\n    const wrapped = await (0,_runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jweAlgorithm, cek, key, iv, new Uint8Array(0));\n    return {\n        encryptedKey: wrapped.ciphertext,\n        iv: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_1__.encode)(wrapped.iv),\n        tag: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_1__.encode)(wrapped.tag),\n    };\n}\nasync function unwrap(alg, key, encryptedKey, iv, tag) {\n    const jweAlgorithm = alg.slice(0, 7);\n    return (0,_runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(jweAlgorithm, key, encryptedKey, iv, tag, new Uint8Array(0));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9hZXNnY21rdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE0QztBQUNBO0FBQ2tCO0FBQ3ZEO0FBQ1A7QUFDQSwwQkFBMEIsK0RBQU87QUFDakM7QUFDQTtBQUNBLFlBQVksNkRBQVM7QUFDckIsYUFBYSw2REFBUztBQUN0QjtBQUNBO0FBQ087QUFDUDtBQUNBLFdBQVcsK0RBQU87QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXENhc2NhZGVQcm9qZWN0c1xcRmx5RmlsZXNcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGFlc2djbWt3LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBlbmNyeXB0IGZyb20gJy4uL3J1bnRpbWUvZW5jcnlwdC5qcyc7XG5pbXBvcnQgZGVjcnlwdCBmcm9tICcuLi9ydW50aW1lL2RlY3J5cHQuanMnO1xuaW1wb3J0IHsgZW5jb2RlIGFzIGJhc2U2NHVybCB9IGZyb20gJy4uL3J1bnRpbWUvYmFzZTY0dXJsLmpzJztcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB3cmFwKGFsZywga2V5LCBjZWssIGl2KSB7XG4gICAgY29uc3QgandlQWxnb3JpdGhtID0gYWxnLnNsaWNlKDAsIDcpO1xuICAgIGNvbnN0IHdyYXBwZWQgPSBhd2FpdCBlbmNyeXB0KGp3ZUFsZ29yaXRobSwgY2VrLCBrZXksIGl2LCBuZXcgVWludDhBcnJheSgwKSk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgZW5jcnlwdGVkS2V5OiB3cmFwcGVkLmNpcGhlcnRleHQsXG4gICAgICAgIGl2OiBiYXNlNjR1cmwod3JhcHBlZC5pdiksXG4gICAgICAgIHRhZzogYmFzZTY0dXJsKHdyYXBwZWQudGFnKSxcbiAgICB9O1xufVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVud3JhcChhbGcsIGtleSwgZW5jcnlwdGVkS2V5LCBpdiwgdGFnKSB7XG4gICAgY29uc3QgandlQWxnb3JpdGhtID0gYWxnLnNsaWNlKDAsIDcpO1xuICAgIHJldHVybiBkZWNyeXB0KGp3ZUFsZ29yaXRobSwga2V5LCBlbmNyeXB0ZWRLZXksIGl2LCB0YWcsIG5ldyBVaW50OEFycmF5KDApKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/aesgcmkw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js":
/*!*************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/buffer_utils.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   concatKdf: () => (/* binding */ concatKdf),\n/* harmony export */   decoder: () => (/* binding */ decoder),\n/* harmony export */   encoder: () => (/* binding */ encoder),\n/* harmony export */   lengthAndInput: () => (/* binding */ lengthAndInput),\n/* harmony export */   p2s: () => (/* binding */ p2s),\n/* harmony export */   uint32be: () => (/* binding */ uint32be),\n/* harmony export */   uint64be: () => (/* binding */ uint64be)\n/* harmony export */ });\n/* harmony import */ var _runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/digest.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js\");\n\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nfunction concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([0]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nfunction uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nfunction uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nfunction lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nasync function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await (0,_runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js":
/*!****************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/cek.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitLength: () => (/* binding */ bitLength),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _runtime_random_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/random.js */ \"node:crypto\");\n\n\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n            return 128;\n        case 'A192GCM':\n            return 192;\n        case 'A256GCM':\n        case 'A128CBC-HS256':\n            return 256;\n        case 'A192CBC-HS384':\n            return 384;\n        case 'A256CBC-HS512':\n            return 512;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg) => (0,_runtime_random_js__WEBPACK_IMPORTED_MODULE_1__.randomFillSync)(new Uint8Array(bitLength(alg) >> 3)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9jZWsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFxRDtBQUNYO0FBQ25DO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQWdCLCtCQUErQixJQUFJO0FBQ3pFO0FBQ0E7QUFDQSxpRUFBZSxTQUFTLGtFQUFNLHFDQUFxQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxjZWsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSk9TRU5vdFN1cHBvcnRlZCB9IGZyb20gJy4uL3V0aWwvZXJyb3JzLmpzJztcbmltcG9ydCByYW5kb20gZnJvbSAnLi4vcnVudGltZS9yYW5kb20uanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGJpdExlbmd0aChhbGcpIHtcbiAgICBzd2l0Y2ggKGFsZykge1xuICAgICAgICBjYXNlICdBMTI4R0NNJzpcbiAgICAgICAgICAgIHJldHVybiAxMjg7XG4gICAgICAgIGNhc2UgJ0ExOTJHQ00nOlxuICAgICAgICAgICAgcmV0dXJuIDE5MjtcbiAgICAgICAgY2FzZSAnQTI1NkdDTSc6XG4gICAgICAgIGNhc2UgJ0ExMjhDQkMtSFMyNTYnOlxuICAgICAgICAgICAgcmV0dXJuIDI1NjtcbiAgICAgICAgY2FzZSAnQTE5MkNCQy1IUzM4NCc6XG4gICAgICAgICAgICByZXR1cm4gMzg0O1xuICAgICAgICBjYXNlICdBMjU2Q0JDLUhTNTEyJzpcbiAgICAgICAgICAgIHJldHVybiA1MTI7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB0aHJvdyBuZXcgSk9TRU5vdFN1cHBvcnRlZChgVW5zdXBwb3J0ZWQgSldFIEFsZ29yaXRobTogJHthbGd9YCk7XG4gICAgfVxufVxuZXhwb3J0IGRlZmF1bHQgKGFsZykgPT4gcmFuZG9tKG5ldyBVaW50OEFycmF5KGJpdExlbmd0aChhbGcpID4+IDMpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/check_iv_length.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/check_iv_length.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _iv_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./iv.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/iv.js\");\n\n\nconst checkIvLength = (enc, iv) => {\n    if (iv.length << 3 !== (0,_iv_js__WEBPACK_IMPORTED_MODULE_0__.bitLength)(enc)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Invalid Initialization Vector length');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (checkIvLength);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9jaGVja19pdl9sZW5ndGguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStDO0FBQ1g7QUFDcEM7QUFDQSwyQkFBMkIsaURBQVM7QUFDcEMsa0JBQWtCLHVEQUFVO0FBQzVCO0FBQ0E7QUFDQSxpRUFBZSxhQUFhLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXENhc2NhZGVQcm9qZWN0c1xcRmx5RmlsZXNcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGNoZWNrX2l2X2xlbmd0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBKV0VJbnZhbGlkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuaW1wb3J0IHsgYml0TGVuZ3RoIH0gZnJvbSAnLi9pdi5qcyc7XG5jb25zdCBjaGVja0l2TGVuZ3RoID0gKGVuYywgaXYpID0+IHtcbiAgICBpZiAoaXYubGVuZ3RoIDw8IDMgIT09IGJpdExlbmd0aChlbmMpKSB7XG4gICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKCdJbnZhbGlkIEluaXRpYWxpemF0aW9uIFZlY3RvciBsZW5ndGgnKTtcbiAgICB9XG59O1xuZXhwb3J0IGRlZmF1bHQgY2hlY2tJdkxlbmd0aDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/check_iv_length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/check_key_type.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkKeyTypeWithJwk: () => (/* binding */ checkKeyTypeWithJwk),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n/* harmony import */ var _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_jwk.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n\n\n\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined && key.use !== 'sig') {\n        throw new TypeError('Invalid key for this operation, when present its use must be sig');\n    }\n    if (key.key_ops !== undefined && key.key_ops.includes?.(usage) !== true) {\n        throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${usage}`);\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, when present its alg must be ${alg}`);\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (allowJwk && _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isJWK(key)) {\n        if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!(0,_runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__.withAlg)(alg, key, ..._runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__.types, 'Uint8Array', allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (allowJwk && _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isJWK(key)) {\n        switch (usage) {\n            case 'sign':\n                if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'verify':\n                if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!(0,_runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__.withAlg)(alg, key, ..._runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__.types, allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (usage === 'sign' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n    }\n    if (usage === 'decrypt' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n    }\n    if (key.algorithm && usage === 'verify' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n    }\n    if (key.algorithm && usage === 'encrypt' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n    }\n};\nfunction checkKeyType(allowJwk, alg, key, usage) {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A\\d{3}(?:GCM)?KW$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (checkKeyType.bind(undefined, false));\nconst checkKeyTypeWithJwk = checkKeyType.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/check_p2s.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/check_p2s.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ checkP2s)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction checkP2s(p2s) {\n    if (!(p2s instanceof Uint8Array) || p2s.length < 8) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('PBES2 Salt Input must be 8 or more octets');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9jaGVja19wMnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7QUFDaEM7QUFDZjtBQUNBLGtCQUFrQix1REFBVTtBQUM1QjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxjaGVja19wMnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSldFSW52YWxpZCB9IGZyb20gJy4uL3V0aWwvZXJyb3JzLmpzJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNoZWNrUDJzKHAycykge1xuICAgIGlmICghKHAycyBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpIHx8IHAycy5sZW5ndGggPCA4KSB7XG4gICAgICAgIHRocm93IG5ldyBKV0VJbnZhbGlkKCdQQkVTMiBTYWx0IElucHV0IG11c3QgYmUgOCBvciBtb3JlIG9jdGV0cycpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/check_p2s.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/crypto_key.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEncCryptoKey: () => (/* binding */ checkEncCryptoKey),\n/* harmony export */   checkSigCryptoKey: () => (/* binding */ checkSigCryptoKey)\n/* harmony export */ });\nfunction unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usages) {\n    if (usages.length && !usages.some((expected) => key.usages.includes(expected))) {\n        let msg = 'CryptoKey does not support this operation, its usages must include ';\n        if (usages.length > 2) {\n            const last = usages.pop();\n            msg += `one of ${usages.join(', ')}, or ${last}.`;\n        }\n        else if (usages.length === 2) {\n            msg += `one of ${usages[0]} or ${usages[1]}.`;\n        }\n        else {\n            msg += `${usages[0]}.`;\n        }\n        throw new TypeError(msg);\n    }\n}\nfunction checkSigCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'EdDSA': {\n            if (key.algorithm.name !== 'Ed25519' && key.algorithm.name !== 'Ed448') {\n                throw unusable('Ed25519 or Ed448');\n            }\n            break;\n        }\n        case 'Ed25519': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\nfunction checkEncCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                case 'X448':\n                    break;\n                default:\n                    throw unusable('ECDH, X25519, or X448');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/decrypt_key_management.js":
/*!***********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/decrypt_key_management.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../runtime/aeskw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js\");\n/* harmony import */ var _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../runtime/ecdhes.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ecdhes.js\");\n/* harmony import */ var _runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../runtime/pbes2kw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/pbes2kw.js\");\n/* harmony import */ var _runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../runtime/rsaes.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/rsaes.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/normalize_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/normalize_key.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/cek.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js\");\n/* harmony import */ var _key_import_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../key/import.js */ \"(rsc)/./node_modules/jose/dist/node/esm/key/import.js\");\n/* harmony import */ var _check_key_type_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check_key_type.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _aesgcmkw_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./aesgcmkw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/aesgcmkw.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nasync function decryptKeyManagement(alg, key, encryptedKey, joseHeader, options) {\n    (0,_check_key_type_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(alg, key, 'decrypt');\n    key = (await _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].normalizePrivateKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            if (encryptedKey !== undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n            return key;\n        }\n        case 'ECDH-ES':\n            if (encryptedKey !== undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!(0,_is_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(joseHeader.epk))\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"epk\" (Ephemeral Public Key) missing or invalid`);\n            if (!_runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_4__.ecdhAllowed(key))\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            const epk = await (0,_key_import_js__WEBPACK_IMPORTED_MODULE_5__.importJWK)(joseHeader.epk, alg);\n            let partyUInfo;\n            let partyVInfo;\n            if (joseHeader.apu !== undefined) {\n                if (typeof joseHeader.apu !== 'string')\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"apu\" (Agreement PartyUInfo) invalid`);\n                try {\n                    partyUInfo = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.apu);\n                }\n                catch {\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the apu');\n                }\n            }\n            if (joseHeader.apv !== undefined) {\n                if (typeof joseHeader.apv !== 'string')\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"apv\" (Agreement PartyVInfo) invalid`);\n                try {\n                    partyVInfo = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.apv);\n                }\n                catch {\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the apv');\n                }\n            }\n            const sharedSecret = await _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_4__.deriveKey(epk, key, alg === 'ECDH-ES' ? joseHeader.enc : alg, alg === 'ECDH-ES' ? (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_7__.bitLength)(joseHeader.enc) : parseInt(alg.slice(-5, -2), 10), partyUInfo, partyVInfo);\n            if (alg === 'ECDH-ES')\n                return sharedSecret;\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            return (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_8__.unwrap)(alg.slice(-6), sharedSecret, encryptedKey);\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            return (0,_runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_9__.decrypt)(alg, key, encryptedKey);\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.p2c !== 'number')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) missing or invalid`);\n            const p2cLimit = options?.maxPBES2Count || 10_000;\n            if (joseHeader.p2c > p2cLimit)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) out is of acceptable bounds`);\n            if (typeof joseHeader.p2s !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"p2s\" (PBES2 Salt) missing or invalid`);\n            let p2s;\n            try {\n                p2s = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.p2s);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the p2s');\n            }\n            return (0,_runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_10__.decrypt)(alg, key, encryptedKey, joseHeader.p2c, p2s);\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            return (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_8__.unwrap)(alg, key, encryptedKey);\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.iv !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"iv\" (Initialization Vector) missing or invalid`);\n            if (typeof joseHeader.tag !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"tag\" (Authentication Tag) missing or invalid`);\n            let iv;\n            try {\n                iv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.iv);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the iv');\n            }\n            let tag;\n            try {\n                tag = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.tag);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the tag');\n            }\n            return (0,_aesgcmkw_js__WEBPACK_IMPORTED_MODULE_11__.unwrap)(alg, key, encryptedKey, iv, tag);\n        }\n        default: {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (decryptKeyManagement);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/decrypt_key_management.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/encrypt_key_management.js":
/*!***********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/encrypt_key_management.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../runtime/aeskw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js\");\n/* harmony import */ var _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../runtime/ecdhes.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ecdhes.js\");\n/* harmony import */ var _runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../runtime/pbes2kw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/pbes2kw.js\");\n/* harmony import */ var _runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../runtime/rsaes.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/rsaes.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/normalize_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/normalize_key.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/cek.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/cek.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _key_export_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../key/export.js */ \"(rsc)/./node_modules/jose/dist/node/esm/key/export.js\");\n/* harmony import */ var _check_key_type_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check_key_type.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./aesgcmkw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/aesgcmkw.js\");\n\n\n\n\n\n\n\n\n\n\n\nasync function encryptKeyManagement(alg, enc, key, providedCek, providedParameters = {}) {\n    let encryptedKey;\n    let parameters;\n    let cek;\n    (0,_check_key_type_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(alg, key, 'encrypt');\n    key = (await _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].normalizePublicKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            cek = key;\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!_runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__.ecdhAllowed(key)) {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            }\n            const { apu, apv } = providedParameters;\n            let { epk: ephemeralKey } = providedParameters;\n            ephemeralKey ||= (await _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__.generateEpk(key)).privateKey;\n            const { x, y, crv, kty } = await (0,_key_export_js__WEBPACK_IMPORTED_MODULE_4__.exportJWK)(ephemeralKey);\n            const sharedSecret = await _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__.deriveKey(key, ephemeralKey, alg === 'ECDH-ES' ? enc : alg, alg === 'ECDH-ES' ? (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__.bitLength)(enc) : parseInt(alg.slice(-5, -2), 10), apu, apv);\n            parameters = { epk: { x, crv, kty } };\n            if (kty === 'EC')\n                parameters.epk.y = y;\n            if (apu)\n                parameters.apu = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(apu);\n            if (apv)\n                parameters.apv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(apv);\n            if (alg === 'ECDH-ES') {\n                cek = sharedSecret;\n                break;\n            }\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const kwAlg = alg.slice(-6);\n            encryptedKey = await (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_7__.wrap)(kwAlg, sharedSecret, cek);\n            break;\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            encryptedKey = await (0,_runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_8__.encrypt)(alg, key, cek);\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const { p2c, p2s } = providedParameters;\n            ({ encryptedKey, ...parameters } = await (0,_runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__.encrypt)(alg, key, cek, p2c, p2s));\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            encryptedKey = await (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_7__.wrap)(alg, key, cek);\n            break;\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const { iv } = providedParameters;\n            ({ encryptedKey, ...parameters } = await (0,_aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__.wrap)(alg, key, cek, iv));\n            break;\n        }\n        default: {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n    return { cek, encryptedKey, parameters };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (encryptKeyManagement);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/encrypt_key_management.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/epoch.js":
/*!******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/epoch.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((date) => Math.floor(date.getTime() / 1000));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9lcG9jaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsMkNBQTJDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXENhc2NhZGVQcm9qZWN0c1xcRmx5RmlsZXNcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGVwb2NoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IChkYXRlKSA9PiBNYXRoLmZsb29yKGRhdGUuZ2V0VGltZSgpIC8gMTAwMCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/epoch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/invalid_key_input.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   withAlg: () => (/* binding */ withAlg)\n/* harmony export */ });\nfunction message(msg, actual, ...types) {\n    types = types.filter(Boolean);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n});\nfunction withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/is_disjoint.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst isDisjoint = (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isDisjoint);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9pc19kaXNqb2ludC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsVUFBVSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxpc19kaXNqb2ludC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc0Rpc2pvaW50ID0gKC4uLmhlYWRlcnMpID0+IHtcbiAgICBjb25zdCBzb3VyY2VzID0gaGVhZGVycy5maWx0ZXIoQm9vbGVhbik7XG4gICAgaWYgKHNvdXJjZXMubGVuZ3RoID09PSAwIHx8IHNvdXJjZXMubGVuZ3RoID09PSAxKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBsZXQgYWNjO1xuICAgIGZvciAoY29uc3QgaGVhZGVyIG9mIHNvdXJjZXMpIHtcbiAgICAgICAgY29uc3QgcGFyYW1ldGVycyA9IE9iamVjdC5rZXlzKGhlYWRlcik7XG4gICAgICAgIGlmICghYWNjIHx8IGFjYy5zaXplID09PSAwKSB7XG4gICAgICAgICAgICBhY2MgPSBuZXcgU2V0KHBhcmFtZXRlcnMpO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChjb25zdCBwYXJhbWV0ZXIgb2YgcGFyYW1ldGVycykge1xuICAgICAgICAgICAgaWYgKGFjYy5oYXMocGFyYW1ldGVyKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFjYy5hZGQocGFyYW1ldGVyKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn07XG5leHBvcnQgZGVmYXVsdCBpc0Rpc2pvaW50O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/is_disjoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js":
/*!*******************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/is_jwk.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isJWK: () => (/* binding */ isJWK),\n/* harmony export */   isPrivateJWK: () => (/* binding */ isPrivateJWK),\n/* harmony export */   isPublicJWK: () => (/* binding */ isPublicJWK),\n/* harmony export */   isSecretJWK: () => (/* binding */ isSecretJWK)\n/* harmony export */ });\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n\nfunction isJWK(key) {\n    return (0,_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(key) && typeof key.kty === 'string';\n}\nfunction isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nfunction isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nfunction isSecretJWK(key) {\n    return isJWK(key) && key.kty === 'oct' && typeof key.k === 'string';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9pc19qd2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0M7QUFDL0I7QUFDUCxXQUFXLHlEQUFRO0FBQ25CO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxpc19qd2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGlzT2JqZWN0IGZyb20gJy4vaXNfb2JqZWN0LmpzJztcbmV4cG9ydCBmdW5jdGlvbiBpc0pXSyhrZXkpIHtcbiAgICByZXR1cm4gaXNPYmplY3Qoa2V5KSAmJiB0eXBlb2Yga2V5Lmt0eSA9PT0gJ3N0cmluZyc7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNQcml2YXRlSldLKGtleSkge1xuICAgIHJldHVybiBrZXkua3R5ICE9PSAnb2N0JyAmJiB0eXBlb2Yga2V5LmQgPT09ICdzdHJpbmcnO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzUHVibGljSldLKGtleSkge1xuICAgIHJldHVybiBrZXkua3R5ICE9PSAnb2N0JyAmJiB0eXBlb2Yga2V5LmQgPT09ICd1bmRlZmluZWQnO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzU2VjcmV0SldLKGtleSkge1xuICAgIHJldHVybiBpc0pXSyhrZXkpICYmIGtleS5rdHkgPT09ICdvY3QnICYmIHR5cGVvZiBrZXkuayA9PT0gJ3N0cmluZyc7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/is_object.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isObject)\n/* harmony export */ });\nfunction isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nfunction isObject(input) {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9pc19vYmplY3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxpc19vYmplY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gaXNPYmplY3RMaWtlKHZhbHVlKSB7XG4gICAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcgJiYgdmFsdWUgIT09IG51bGw7XG59XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBpc09iamVjdChpbnB1dCkge1xuICAgIGlmICghaXNPYmplY3RMaWtlKGlucHV0KSB8fCBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwoaW5wdXQpICE9PSAnW29iamVjdCBPYmplY3RdJykge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChPYmplY3QuZ2V0UHJvdG90eXBlT2YoaW5wdXQpID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBsZXQgcHJvdG8gPSBpbnB1dDtcbiAgICB3aGlsZSAoT2JqZWN0LmdldFByb3RvdHlwZU9mKHByb3RvKSAhPT0gbnVsbCkge1xuICAgICAgICBwcm90byA9IE9iamVjdC5nZXRQcm90b3R5cGVPZihwcm90byk7XG4gICAgfVxuICAgIHJldHVybiBPYmplY3QuZ2V0UHJvdG90eXBlT2YoaW5wdXQpID09PSBwcm90bztcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/iv.js":
/*!***************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/iv.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitLength: () => (/* binding */ bitLength),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _runtime_random_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/random.js */ \"node:crypto\");\n\n\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A128GCMKW':\n        case 'A192GCM':\n        case 'A192GCMKW':\n        case 'A256GCM':\n        case 'A256GCMKW':\n            return 96;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return 128;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg) => (0,_runtime_random_js__WEBPACK_IMPORTED_MODULE_1__.randomFillSync)(new Uint8Array(bitLength(alg) >> 3)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9pdi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXFEO0FBQ1g7QUFDbkM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBZ0IsK0JBQStCLElBQUk7QUFDekU7QUFDQTtBQUNBLGlFQUFlLFNBQVMsa0VBQU0scUNBQXFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXENhc2NhZGVQcm9qZWN0c1xcRmx5RmlsZXNcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGl2LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEpPU0VOb3RTdXBwb3J0ZWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5pbXBvcnQgcmFuZG9tIGZyb20gJy4uL3J1bnRpbWUvcmFuZG9tLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBiaXRMZW5ndGgoYWxnKSB7XG4gICAgc3dpdGNoIChhbGcpIHtcbiAgICAgICAgY2FzZSAnQTEyOEdDTSc6XG4gICAgICAgIGNhc2UgJ0ExMjhHQ01LVyc6XG4gICAgICAgIGNhc2UgJ0ExOTJHQ00nOlxuICAgICAgICBjYXNlICdBMTkyR0NNS1cnOlxuICAgICAgICBjYXNlICdBMjU2R0NNJzpcbiAgICAgICAgY2FzZSAnQTI1NkdDTUtXJzpcbiAgICAgICAgICAgIHJldHVybiA5NjtcbiAgICAgICAgY2FzZSAnQTEyOENCQy1IUzI1Nic6XG4gICAgICAgIGNhc2UgJ0ExOTJDQkMtSFMzODQnOlxuICAgICAgICBjYXNlICdBMjU2Q0JDLUhTNTEyJzpcbiAgICAgICAgICAgIHJldHVybiAxMjg7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB0aHJvdyBuZXcgSk9TRU5vdFN1cHBvcnRlZChgVW5zdXBwb3J0ZWQgSldFIEFsZ29yaXRobTogJHthbGd9YCk7XG4gICAgfVxufVxuZXhwb3J0IGRlZmF1bHQgKGFsZykgPT4gcmFuZG9tKG5ldyBVaW50OEFycmF5KGJpdExlbmd0aChhbGcpID4+IDMpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/iv.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/jwt_claims_set.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/jwt_claims_set.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _epoch_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./epoch.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/epoch.js\");\n/* harmony import */ var _secs_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./secs.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/secs.js\");\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n\n\n\n\n\nconst normalizeTyp = (value) => value.toLowerCase().replace(/^application\\//, '');\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((protectedHeader, encodedPayload, options = {}) => {\n    let payload;\n    try {\n        payload = JSON.parse(_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!(0,_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(payload)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/jwt_claims_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/private_symbols.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/private_symbols.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unprotected: () => (/* binding */ unprotected)\n/* harmony export */ });\nconst unprotected = Symbol();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9wcml2YXRlX3N5bWJvbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxwcml2YXRlX3N5bWJvbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVucHJvdGVjdGVkID0gU3ltYm9sKCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/private_symbols.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/secs.js":
/*!*****************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/secs.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/secs.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/validate_algorithms.js":
/*!********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/validate_algorithms.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst validateAlgorithms = (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validateAlgorithms);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi92YWxpZGF0ZV9hbGdvcml0aG1zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsT0FBTztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBZSxrQkFBa0IsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcdmFsaWRhdGVfYWxnb3JpdGhtcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB2YWxpZGF0ZUFsZ29yaXRobXMgPSAob3B0aW9uLCBhbGdvcml0aG1zKSA9PiB7XG4gICAgaWYgKGFsZ29yaXRobXMgIT09IHVuZGVmaW5lZCAmJlxuICAgICAgICAoIUFycmF5LmlzQXJyYXkoYWxnb3JpdGhtcykgfHwgYWxnb3JpdGhtcy5zb21lKChzKSA9PiB0eXBlb2YgcyAhPT0gJ3N0cmluZycpKSkge1xuICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGBcIiR7b3B0aW9ufVwiIG9wdGlvbiBtdXN0IGJlIGFuIGFycmF5IG9mIHN0cmluZ3NgKTtcbiAgICB9XG4gICAgaWYgKCFhbGdvcml0aG1zKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIHJldHVybiBuZXcgU2V0KGFsZ29yaXRobXMpO1xufTtcbmV4cG9ydCBkZWZhdWx0IHZhbGlkYXRlQWxnb3JpdGhtcztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/validate_algorithms.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/validate_crit.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction validateCrit(Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validateCrit);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/validate_crit.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/aeskw.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _ciphers_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ciphers.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\nfunction checkKeySize(key, alg) {\n    if (key.symmetricKeySize << 3 !== parseInt(alg.slice(1, 4), 10)) {\n        throw new TypeError(`Invalid key size for alg: ${alg}`);\n    }\n}\nfunction ensureKeyObject(key, alg, usage) {\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        return key;\n    }\n    if (key instanceof Uint8Array) {\n        return (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.createSecretKey)(key);\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_3__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__.checkEncCryptoKey)(key, alg, usage);\n        return node_crypto__WEBPACK_IMPORTED_MODULE_1__.KeyObject.from(key);\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types, 'Uint8Array'));\n}\nconst wrap = (alg, key, cek) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_8__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.createCipheriv)(algorithm, keyObject, node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.alloc(8, 0xa6));\n    return (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_9__.concat)(cipher.update(cek), cipher.final());\n};\nconst unwrap = (alg, key, encryptedKey) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_8__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.createDecipheriv)(algorithm, keyObject, node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.alloc(8, 0xa6));\n    return (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_9__.concat)(cipher.update(encryptedKey), cipher.final());\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/asn1.js":
/*!*********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/asn1.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromPKCS8: () => (/* binding */ fromPKCS8),\n/* harmony export */   fromSPKI: () => (/* binding */ fromSPKI),\n/* harmony export */   fromX509: () => (/* binding */ fromX509),\n/* harmony export */   toPKCS8: () => (/* binding */ toPKCS8),\n/* harmony export */   toSPKI: () => (/* binding */ toSPKI)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\nconst genericExport = (keyType, keyFormat, key) => {\n    let keyObject;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(key)) {\n        keyObject = key;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_5__.types));\n    }\n    if (keyObject.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return keyObject.export({ format: 'pem', type: keyFormat });\n};\nconst toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nconst toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nconst fromPKCS8 = (pem) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPrivateKey)({\n    key: node_buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g, ''), 'base64'),\n    type: 'pkcs8',\n    format: 'der',\n});\nconst fromSPKI = (pem) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPublicKey)({\n    key: node_buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g, ''), 'base64'),\n    type: 'spki',\n    format: 'der',\n});\nconst fromX509 = (pem) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPublicKey)({\n    key: pem,\n    type: 'spki',\n    format: 'pem',\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/asn1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/base64url.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64)\n/* harmony export */ });\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.decoder.decode(encoded);\n    }\n    return encoded;\n}\nconst encode = (input) => node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString('base64url');\nconst decodeBase64 = (input) => new Uint8Array(node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input, 'base64'));\nconst encodeBase64 = (input) => node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString('base64');\n\nconst decode = (input) => new Uint8Array(node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(normalize(input), 'base64url'));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvYmFzZTY0dXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFxQztBQUNZO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix5REFBTztBQUN6QjtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsK0NBQU07QUFDekIsK0NBQStDLCtDQUFNO0FBQ3JELGdDQUFnQywrQ0FBTTtBQUMzQjtBQUNYLHlDQUF5QywrQ0FBTSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGJhc2U2NHVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCdWZmZXIgfSBmcm9tICdub2RlOmJ1ZmZlcic7XG5pbXBvcnQgeyBkZWNvZGVyIH0gZnJvbSAnLi4vbGliL2J1ZmZlcl91dGlscy5qcyc7XG5mdW5jdGlvbiBub3JtYWxpemUoaW5wdXQpIHtcbiAgICBsZXQgZW5jb2RlZCA9IGlucHV0O1xuICAgIGlmIChlbmNvZGVkIGluc3RhbmNlb2YgVWludDhBcnJheSkge1xuICAgICAgICBlbmNvZGVkID0gZGVjb2Rlci5kZWNvZGUoZW5jb2RlZCk7XG4gICAgfVxuICAgIHJldHVybiBlbmNvZGVkO1xufVxuY29uc3QgZW5jb2RlID0gKGlucHV0KSA9PiBCdWZmZXIuZnJvbShpbnB1dCkudG9TdHJpbmcoJ2Jhc2U2NHVybCcpO1xuZXhwb3J0IGNvbnN0IGRlY29kZUJhc2U2NCA9IChpbnB1dCkgPT4gbmV3IFVpbnQ4QXJyYXkoQnVmZmVyLmZyb20oaW5wdXQsICdiYXNlNjQnKSk7XG5leHBvcnQgY29uc3QgZW5jb2RlQmFzZTY0ID0gKGlucHV0KSA9PiBCdWZmZXIuZnJvbShpbnB1dCkudG9TdHJpbmcoJ2Jhc2U2NCcpO1xuZXhwb3J0IHsgZW5jb2RlIH07XG5leHBvcnQgY29uc3QgZGVjb2RlID0gKGlucHV0KSA9PiBuZXcgVWludDhBcnJheShCdWZmZXIuZnJvbShub3JtYWxpemUoaW5wdXQpLCAnYmFzZTY0dXJsJykpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/cbc_tag.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/cbc_tag.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cbcTag)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\nfunction cbcTag(aad, iv, ciphertext, macSize, macKey, keySize) {\n    const macData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.concat)(aad, iv, ciphertext, (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.uint64be)(aad.length << 3));\n    const hmac = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createHmac)(`sha${macSize}`, macKey);\n    hmac.update(macData);\n    return hmac.digest().slice(0, keySize >> 3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvY2JjX3RhZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUM7QUFDaUI7QUFDM0M7QUFDZixvQkFBb0IsNERBQU0sc0JBQXNCLDhEQUFRO0FBQ3hELGlCQUFpQix1REFBVSxPQUFPLFFBQVE7QUFDMUM7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcY2JjX3RhZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVIbWFjIH0gZnJvbSAnbm9kZTpjcnlwdG8nO1xuaW1wb3J0IHsgY29uY2F0LCB1aW50NjRiZSB9IGZyb20gJy4uL2xpYi9idWZmZXJfdXRpbHMuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY2JjVGFnKGFhZCwgaXYsIGNpcGhlcnRleHQsIG1hY1NpemUsIG1hY0tleSwga2V5U2l6ZSkge1xuICAgIGNvbnN0IG1hY0RhdGEgPSBjb25jYXQoYWFkLCBpdiwgY2lwaGVydGV4dCwgdWludDY0YmUoYWFkLmxlbmd0aCA8PCAzKSk7XG4gICAgY29uc3QgaG1hYyA9IGNyZWF0ZUhtYWMoYHNoYSR7bWFjU2l6ZX1gLCBtYWNLZXkpO1xuICAgIGhtYWMudXBkYXRlKG1hY0RhdGEpO1xuICAgIHJldHVybiBobWFjLmRpZ2VzdCgpLnNsaWNlKDAsIGtleVNpemUgPj4gMyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/cbc_tag.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/check_cek_length.js":
/*!*********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/check_cek_length.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n\n\nconst checkCekLength = (enc, cek) => {\n    let expected;\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            expected = parseInt(enc.slice(-3), 10);\n            break;\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            expected = parseInt(enc.slice(1, 4), 10);\n            break;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Content Encryption Algorithm ${enc} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (cek instanceof Uint8Array) {\n        const actual = cek.byteLength << 3;\n        if (actual !== expected) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek) && cek.type === 'secret') {\n        const actual = cek.symmetricKeySize << 3;\n        if (actual !== expected) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    throw new TypeError('Invalid Content Encryption Key type');\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (checkCekLength);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/check_cek_length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/check_key_length.js":
/*!*********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/check_key_length.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((key, alg) => {\n    let modulusLength;\n    try {\n        if (key instanceof node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject) {\n            modulusLength = key.asymmetricKeyDetails?.modulusLength;\n        }\n        else {\n            modulusLength = Buffer.from(key.n, 'base64url').byteLength << 3;\n        }\n    }\n    catch { }\n    if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n        throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvY2hlY2tfa2V5X2xlbmd0aC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUN4QyxpRUFBZTtBQUNmO0FBQ0E7QUFDQSwyQkFBMkIsa0RBQVM7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixLQUFLO0FBQ3BDO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcY2hlY2tfa2V5X2xlbmd0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBLZXlPYmplY3QgfSBmcm9tICdub2RlOmNyeXB0byc7XG5leHBvcnQgZGVmYXVsdCAoa2V5LCBhbGcpID0+IHtcbiAgICBsZXQgbW9kdWx1c0xlbmd0aDtcbiAgICB0cnkge1xuICAgICAgICBpZiAoa2V5IGluc3RhbmNlb2YgS2V5T2JqZWN0KSB7XG4gICAgICAgICAgICBtb2R1bHVzTGVuZ3RoID0ga2V5LmFzeW1tZXRyaWNLZXlEZXRhaWxzPy5tb2R1bHVzTGVuZ3RoO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgbW9kdWx1c0xlbmd0aCA9IEJ1ZmZlci5mcm9tKGtleS5uLCAnYmFzZTY0dXJsJykuYnl0ZUxlbmd0aCA8PCAzO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNhdGNoIHsgfVxuICAgIGlmICh0eXBlb2YgbW9kdWx1c0xlbmd0aCAhPT0gJ251bWJlcicgfHwgbW9kdWx1c0xlbmd0aCA8IDIwNDgpIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgJHthbGd9IHJlcXVpcmVzIGtleSBtb2R1bHVzTGVuZ3RoIHRvIGJlIDIwNDggYml0cyBvciBsYXJnZXJgKTtcbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/check_key_length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/ciphers.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nlet ciphers;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((algorithm) => {\n    ciphers ||= new Set((0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.getCiphers)());\n    return ciphers.has(algorithm);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvY2lwaGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5QztBQUN6QztBQUNBLGlFQUFlO0FBQ2Ysd0JBQXdCLHVEQUFVO0FBQ2xDO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcY2lwaGVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRDaXBoZXJzIH0gZnJvbSAnbm9kZTpjcnlwdG8nO1xubGV0IGNpcGhlcnM7XG5leHBvcnQgZGVmYXVsdCAoYWxnb3JpdGhtKSA9PiB7XG4gICAgY2lwaGVycyB8fD0gbmV3IFNldChnZXRDaXBoZXJzKCkpO1xuICAgIHJldHVybiBjaXBoZXJzLmhhcyhhbGdvcml0aG0pO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/decrypt.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/decrypt.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../lib/check_iv_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_iv_length.js\");\n/* harmony import */ var _check_cek_length_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./check_cek_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/check_cek_length.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _timing_safe_equal_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./timing_safe_equal.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js\");\n/* harmony import */ var _cbc_tag_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./cbc_tag.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/cbc_tag.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _ciphers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ciphers.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction cbcDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const macSize = parseInt(enc.slice(-3), 10);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const expectedTag = (0,_cbc_tag_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(aad, iv, ciphertext, macSize, macKey, keySize);\n    let macCheckPassed;\n    try {\n        macCheckPassed = (0,_timing_safe_equal_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(tag, expectedTag);\n    }\n    catch {\n    }\n    if (!macCheckPassed) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEDecryptionFailed();\n    }\n    let plaintext;\n    try {\n        const decipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createDecipheriv)(algorithm, encKey, iv);\n        plaintext = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_6__.concat)(decipher.update(ciphertext), decipher.final());\n    }\n    catch {\n    }\n    if (!plaintext) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEDecryptionFailed();\n    }\n    return plaintext;\n}\nfunction gcmDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    try {\n        const decipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createDecipheriv)(algorithm, cek, iv, { authTagLength: 16 });\n        decipher.setAuthTag(tag);\n        if (aad.byteLength) {\n            decipher.setAAD(aad, { plaintextLength: ciphertext.length });\n        }\n        const plaintext = decipher.update(ciphertext);\n        decipher.final();\n        return plaintext;\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEDecryptionFailed();\n    }\n}\nconst decrypt = (enc, cek, ciphertext, iv, tag, aad) => {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_7__.isCryptoKey)(cek)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_8__.checkEncCryptoKey)(cek, enc, 'decrypt');\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || (0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(cek, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_10__.types, 'Uint8Array'));\n    }\n    if (!iv) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEInvalid('JWE Initialization Vector missing');\n    }\n    if (!tag) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEInvalid('JWE Authentication Tag missing');\n    }\n    (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(enc, key);\n    (0,_lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcDecrypt(enc, key, ciphertext, iv, tag, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmDecrypt(enc, key, ciphertext, iv, tag, aad);\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (decrypt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/decrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/digest.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nconst digest = (algorithm, data) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createHash)(algorithm).update(data).digest();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (digest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvZGlnZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlDO0FBQ3pDLG9DQUFvQyx1REFBVTtBQUM5QyxpRUFBZSxNQUFNLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXENhc2NhZGVQcm9qZWN0c1xcRmx5RmlsZXNcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxkaWdlc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlSGFzaCB9IGZyb20gJ25vZGU6Y3J5cHRvJztcbmNvbnN0IGRpZ2VzdCA9IChhbGdvcml0aG0sIGRhdGEpID0+IGNyZWF0ZUhhc2goYWxnb3JpdGhtKS51cGRhdGUoZGF0YSkuZGlnZXN0KCk7XG5leHBvcnQgZGVmYXVsdCBkaWdlc3Q7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/ecdhes.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/ecdhes.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deriveKey: () => (/* binding */ deriveKey),\n/* harmony export */   ecdhAllowed: () => (/* binding */ ecdhAllowed),\n/* harmony export */   generateEpk: () => (/* binding */ generateEpk)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _get_named_curve_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./get_named_curve.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/get_named_curve.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\nconst generateKeyPair = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_0__.generateKeyPair);\nasync function deriveKey(publicKee, privateKee, algorithm, keyLength, apu = new Uint8Array(0), apv = new Uint8Array(0)) {\n    let publicKey;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(publicKee)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__.checkEncCryptoKey)(publicKee, 'ECDH');\n        publicKey = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(publicKee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(publicKee)) {\n        publicKey = publicKee;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(publicKee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    let privateKey;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(privateKee)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__.checkEncCryptoKey)(privateKee, 'ECDH', 'deriveBits');\n        privateKey = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(privateKee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(privateKee)) {\n        privateKey = privateKee;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(privateKee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    const value = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.concat)((0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.lengthAndInput)(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.encoder.encode(algorithm)), (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.lengthAndInput)(apu), (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.lengthAndInput)(apv), (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.uint32be)(keyLength));\n    const sharedSecret = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.diffieHellman)({ privateKey, publicKey });\n    return (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.concatKdf)(sharedSecret, keyLength, value);\n}\nasync function generateEpk(kee) {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(kee)) {\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(kee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(kee)) {\n        key = kee;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(kee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    switch (key.asymmetricKeyType) {\n        case 'x25519':\n            return generateKeyPair('x25519');\n        case 'x448': {\n            return generateKeyPair('x448');\n        }\n        case 'ec': {\n            const namedCurve = (0,_get_named_curve_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(key);\n            return generateKeyPair('ec', { namedCurve });\n        }\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_9__.JOSENotSupported('Invalid or unsupported EPK');\n    }\n}\nconst ecdhAllowed = (key) => ['P-256', 'P-384', 'P-521', 'X25519', 'X448'].includes((0,_get_named_curve_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(key));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/ecdhes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/encrypt.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/encrypt.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../lib/check_iv_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_iv_length.js\");\n/* harmony import */ var _check_cek_length_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./check_cek_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/check_cek_length.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _cbc_tag_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./cbc_tag.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/cbc_tag.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _lib_iv_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../lib/iv.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/iv.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _ciphers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ciphers.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/ciphers.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction cbcEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createCipheriv)(algorithm, encKey, iv);\n    const ciphertext = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.concat)(cipher.update(plaintext), cipher.final());\n    const macSize = parseInt(enc.slice(-3), 10);\n    const tag = (0,_cbc_tag_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(aad, iv, ciphertext, macSize, macKey, keySize);\n    return { ciphertext, tag, iv };\n}\nfunction gcmEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createCipheriv)(algorithm, cek, iv, { authTagLength: 16 });\n    if (aad.byteLength) {\n        cipher.setAAD(aad, { plaintextLength: plaintext.length });\n    }\n    const ciphertext = cipher.update(plaintext);\n    cipher.final();\n    const tag = cipher.getAuthTag();\n    return { ciphertext, tag, iv };\n}\nconst encrypt = (enc, plaintext, cek, iv, aad) => {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_6__.isCryptoKey)(cek)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_7__.checkEncCryptoKey)(cek, enc, 'encrypt');\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || (0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(cek, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_9__.types, 'Uint8Array'));\n    }\n    (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(enc, key);\n    if (iv) {\n        (0,_lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(enc, iv);\n    }\n    else {\n        iv = (0,_lib_iv_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(enc);\n    }\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcEncrypt(enc, plaintext, key, iv, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmEncrypt(enc, plaintext, key, iv, aad);\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (encrypt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/encrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/get_named_curve.js":
/*!********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/get_named_curve.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   weakMap: () => (/* binding */ weakMap)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n/* harmony import */ var _lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/is_jwk.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n\n\n\n\n\n\n\nconst weakMap = new WeakMap();\nconst namedCurveToJOSE = (namedCurve) => {\n    switch (namedCurve) {\n        case 'prime256v1':\n            return 'P-256';\n        case 'secp384r1':\n            return 'P-384';\n        case 'secp521r1':\n            return 'P-521';\n        case 'secp256k1':\n            return 'secp256k1';\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported('Unsupported key curve for this operation');\n    }\n};\nconst getNamedCurve = (kee, raw) => {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(kee)) {\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(kee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(kee)) {\n        key = kee;\n    }\n    else if ((0,_lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_4__.isJWK)(kee)) {\n        return kee.crv;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(kee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError('only \"private\" or \"public\" type keys can be used for this operation');\n    }\n    switch (key.asymmetricKeyType) {\n        case 'ed25519':\n        case 'ed448':\n            return `Ed${key.asymmetricKeyType.slice(2)}`;\n        case 'x25519':\n        case 'x448':\n            return `X${key.asymmetricKeyType.slice(1)}`;\n        case 'ec': {\n            const namedCurve = key.asymmetricKeyDetails.namedCurve;\n            if (raw) {\n                return namedCurve;\n            }\n            return namedCurveToJOSE(namedCurve);\n        }\n        default:\n            throw new TypeError('Invalid asymmetric key type for this operation');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getNamedCurve);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/get_named_curve.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js":
/*!****************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/is_key_like.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((key) => (0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(key) || (0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_1__.isCryptoKey)(key));\nconst types = ['KeyObject'];\nif (globalThis.CryptoKey || _webcrypto_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]?.CryptoKey) {\n    types.push('CryptoKey');\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvaXNfa2V5X2xpa2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF3RDtBQUNYO0FBQzdDLGlFQUFlLFNBQVMsNkRBQVcsU0FBUywwREFBVyxLQUFLLEVBQUM7QUFDN0Q7QUFDQSw0QkFBNEIscURBQVM7QUFDckM7QUFDQTtBQUNpQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGlzX2tleV9saWtlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB3ZWJjcnlwdG8sIHsgaXNDcnlwdG9LZXkgfSBmcm9tICcuL3dlYmNyeXB0by5qcyc7XG5pbXBvcnQgaXNLZXlPYmplY3QgZnJvbSAnLi9pc19rZXlfb2JqZWN0LmpzJztcbmV4cG9ydCBkZWZhdWx0IChrZXkpID0+IGlzS2V5T2JqZWN0KGtleSkgfHwgaXNDcnlwdG9LZXkoa2V5KTtcbmNvbnN0IHR5cGVzID0gWydLZXlPYmplY3QnXTtcbmlmIChnbG9iYWxUaGlzLkNyeXB0b0tleSB8fCB3ZWJjcnlwdG8/LkNyeXB0b0tleSkge1xuICAgIHR5cGVzLnB1c2goJ0NyeXB0b0tleScpO1xufVxuZXhwb3J0IHsgdHlwZXMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/is_key_object.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:util */ \"node:util\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((obj) => node_util__WEBPACK_IMPORTED_MODULE_0__.types.isKeyObject(obj));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvaXNfa2V5X29iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQztBQUNsQyxpRUFBZSxTQUFTLDRDQUFVLGlCQUFpQixFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcaXNfa2V5X29iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyB1dGlsIGZyb20gJ25vZGU6dXRpbCc7XG5leHBvcnQgZGVmYXVsdCAob2JqKSA9PiB1dGlsLnR5cGVzLmlzS2V5T2JqZWN0KG9iaik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/jwk_to_key.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/jwk_to_key.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nconst parse = (key) => {\n    if (key.d) {\n        return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPrivateKey)({ format: 'jwk', key });\n    }\n    return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPublicKey)({ format: 'jwk', key });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (parse);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvandrX3RvX2tleS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRTtBQUNoRTtBQUNBO0FBQ0EsZUFBZSw2REFBZ0IsR0FBRyxvQkFBb0I7QUFDdEQ7QUFDQSxXQUFXLDREQUFlLEdBQUcsb0JBQW9CO0FBQ2pEO0FBQ0EsaUVBQWUsS0FBSyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcandrX3RvX2tleS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVQcml2YXRlS2V5LCBjcmVhdGVQdWJsaWNLZXkgfSBmcm9tICdub2RlOmNyeXB0byc7XG5jb25zdCBwYXJzZSA9IChrZXkpID0+IHtcbiAgICBpZiAoa2V5LmQpIHtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVByaXZhdGVLZXkoeyBmb3JtYXQ6ICdqd2snLCBrZXkgfSk7XG4gICAgfVxuICAgIHJldHVybiBjcmVhdGVQdWJsaWNLZXkoeyBmb3JtYXQ6ICdqd2snLCBrZXkgfSk7XG59O1xuZXhwb3J0IGRlZmF1bHQgcGFyc2U7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/jwk_to_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/key_to_jwk.js":
/*!***************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/key_to_jwk.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _base64url_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\nconst keyToJWK = (key) => {\n    let keyObject;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_1__.isCryptoKey)(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        keyObject = key;\n    }\n    else if (key instanceof Uint8Array) {\n        return {\n            kty: 'oct',\n            k: (0,_base64url_js__WEBPACK_IMPORTED_MODULE_3__.encode)(key),\n        };\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_5__.types, 'Uint8Array'));\n    }\n    if (keyObject.type !== 'secret' &&\n        !['rsa', 'ec', 'ed25519', 'x25519', 'ed448', 'x448'].includes(keyObject.asymmetricKeyType)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_6__.JOSENotSupported('Unsupported key asymmetricKeyType');\n    }\n    return keyObject.export({ format: 'jwk' });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (keyToJWK);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/key_to_jwk.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/normalize_key.js":
/*!******************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/normalize_key.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvbm9ybWFsaXplX2tleS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsRUFBRSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1pY2thXFxDYXNjYWRlUHJvamVjdHNcXEZseUZpbGVzXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcbm9ybWFsaXplX2tleS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/normalize_key.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/pbes2kw.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/pbes2kw.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./random.js */ \"node:crypto\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _base64url_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _aeskw_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./aeskw.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/aeskw.js\");\n/* harmony import */ var _lib_check_p2s_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/check_p2s.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/check_p2s.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst pbkdf2 = (0,node_util__WEBPACK_IMPORTED_MODULE_0__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_1__.pbkdf2);\nfunction getPassword(key, alg) {\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        return key.export();\n    }\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_3__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__.checkEncCryptoKey)(key, alg, 'deriveBits', 'deriveKey');\n        return node_crypto__WEBPACK_IMPORTED_MODULE_1__.KeyObject.from(key).export();\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types, 'Uint8Array'));\n}\nconst encrypt = async (alg, key, cek, p2c = 2048, p2s = (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.randomFillSync)(new Uint8Array(16))) => {\n    (0,_lib_check_p2s_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(p2s);\n    const salt = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_8__.p2s)(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    const encryptedKey = await (0,_aeskw_js__WEBPACK_IMPORTED_MODULE_9__.wrap)(alg.slice(-6), derivedKey, cek);\n    return { encryptedKey, p2c, p2s: (0,_base64url_js__WEBPACK_IMPORTED_MODULE_10__.encode)(p2s) };\n};\nconst decrypt = async (alg, key, encryptedKey, p2c, p2s) => {\n    (0,_lib_check_p2s_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(p2s);\n    const salt = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_8__.p2s)(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    return (0,_aeskw_js__WEBPACK_IMPORTED_MODULE_9__.unwrap)(alg.slice(-6), derivedKey, encryptedKey);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/pbes2kw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/rsaes.js":
/*!**********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/rsaes.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _check_key_length_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./check_key_length.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/check_key_length.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\nconst checkKey = (key, alg) => {\n    if (key.asymmetricKeyType !== 'rsa') {\n        throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n    }\n    (0,_check_key_length_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key, alg);\n};\nconst RSA1_5 = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.deprecate)(() => node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PKCS1_PADDING, 'The RSA1_5 \"alg\" (JWE Algorithm) is deprecated and will be removed in the next major revision.');\nconst resolvePadding = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            return node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PKCS1_OAEP_PADDING;\n        case 'RSA1_5':\n            return RSA1_5();\n        default:\n            return undefined;\n    }\n};\nconst resolveOaepHash = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n            return 'sha1';\n        case 'RSA-OAEP-256':\n            return 'sha256';\n        case 'RSA-OAEP-384':\n            return 'sha384';\n        case 'RSA-OAEP-512':\n            return 'sha512';\n        default:\n            return undefined;\n    }\n};\nfunction ensureKeyObject(key, alg, ...usages) {\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(key)) {\n        return key;\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_4__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_5__.checkEncCryptoKey)(key, alg, ...usages);\n        return node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_7__.types));\n}\nconst encrypt = (alg, key, cek) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey', 'encrypt');\n    checkKey(keyObject, alg);\n    return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.publicEncrypt)({ key: keyObject, oaepHash, padding }, cek);\n};\nconst decrypt = (alg, key, encryptedKey) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey', 'decrypt');\n    checkKey(keyObject, alg);\n    return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.privateDecrypt)({ key: keyObject, oaepHash, padding }, encryptedKey);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/rsaes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js":
/*!**********************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nconst timingSafeEqual = node_crypto__WEBPACK_IMPORTED_MODULE_0__.timingSafeEqual;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (timingSafeEqual);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvdGltaW5nX3NhZmVfZXF1YWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0Q7QUFDdEQsd0JBQXdCLHdEQUFJO0FBQzVCLGlFQUFlLGVBQWUsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXHRpbWluZ19zYWZlX2VxdWFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHRpbWluZ1NhZmVFcXVhbCBhcyBpbXBsIH0gZnJvbSAnbm9kZTpjcnlwdG8nO1xuY29uc3QgdGltaW5nU2FmZUVxdWFsID0gaW1wbDtcbmV4cG9ydCBkZWZhdWx0IHRpbWluZ1NhZmVFcXVhbDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/webcrypto.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isCryptoKey: () => (/* binding */ isCryptoKey)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n\n\nconst webcrypto = node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (webcrypto);\nconst isCryptoKey = (key) => node_util__WEBPACK_IMPORTED_MODULE_1__.types.isCryptoKey(key);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvd2ViY3J5cHRvLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBc0M7QUFDSjtBQUNsQyxrQkFBa0Isa0RBQWdCO0FBQ2xDLGlFQUFlLFNBQVMsRUFBQztBQUNsQiw2QkFBNkIsNENBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXENhc2NhZGVQcm9qZWN0c1xcRmx5RmlsZXNcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFx3ZWJjcnlwdG8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgY3J5cHRvIGZyb20gJ25vZGU6Y3J5cHRvJztcbmltcG9ydCAqIGFzIHV0aWwgZnJvbSAnbm9kZTp1dGlsJztcbmNvbnN0IHdlYmNyeXB0byA9IGNyeXB0by53ZWJjcnlwdG87XG5leHBvcnQgZGVmYXVsdCB3ZWJjcnlwdG87XG5leHBvcnQgY29uc3QgaXNDcnlwdG9LZXkgPSAoa2V5KSA9PiB1dGlsLnR5cGVzLmlzQ3J5cHRvS2V5KGtleSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/webcrypto.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/util/base64url.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/util/base64url.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n\nconst encode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.encode;\nconst decode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.decode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3V0aWwvYmFzZTY0dXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUM5QyxlQUFlLHlEQUFnQjtBQUMvQixlQUFlLHlEQUFnQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHV0aWxcXGJhc2U2NHVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBiYXNlNjR1cmwgZnJvbSAnLi4vcnVudGltZS9iYXNlNjR1cmwuanMnO1xuZXhwb3J0IGNvbnN0IGVuY29kZSA9IGJhc2U2NHVybC5lbmNvZGU7XG5leHBvcnQgY29uc3QgZGVjb2RlID0gYmFzZTY0dXJsLmRlY29kZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/util/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/util/decode_jwt.js":
/*!************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/util/decode_jwt.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeJwt: () => (/* binding */ decodeJwt)\n/* harmony export */ });\n/* harmony import */ var _base64url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/base64url.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\");\n\n\n\n\nfunction decodeJwt(jwt) {\n    if (typeof jwt !== 'string')\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('JWTs must use Compact JWS serialization, JWT must be a string');\n    const { 1: payload, length } = jwt.split('.');\n    if (length === 5)\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Only JWTs using Compact JWS serialization can be decoded');\n    if (length !== 3)\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Invalid JWT');\n    if (!payload)\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('JWTs must contain a payload');\n    let decoded;\n    try {\n        decoded = (0,_base64url_js__WEBPACK_IMPORTED_MODULE_1__.decode)(payload);\n    }\n    catch {\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Failed to base64url decode the payload');\n    }\n    let result;\n    try {\n        result = JSON.parse(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__.decoder.decode(decoded));\n    }\n    catch {\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Failed to parse the decoded payload as JSON');\n    }\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(result))\n        throw new _errors_js__WEBPACK_IMPORTED_MODULE_0__.JWTInvalid('Invalid JWT Claims Set');\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/util/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/util/errors.js":
/*!********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/util/errors.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JOSEAlgNotAllowed: () => (/* binding */ JOSEAlgNotAllowed),\n/* harmony export */   JOSEError: () => (/* binding */ JOSEError),\n/* harmony export */   JOSENotSupported: () => (/* binding */ JOSENotSupported),\n/* harmony export */   JWEDecryptionFailed: () => (/* binding */ JWEDecryptionFailed),\n/* harmony export */   JWEInvalid: () => (/* binding */ JWEInvalid),\n/* harmony export */   JWKInvalid: () => (/* binding */ JWKInvalid),\n/* harmony export */   JWKSInvalid: () => (/* binding */ JWKSInvalid),\n/* harmony export */   JWKSMultipleMatchingKeys: () => (/* binding */ JWKSMultipleMatchingKeys),\n/* harmony export */   JWKSNoMatchingKey: () => (/* binding */ JWKSNoMatchingKey),\n/* harmony export */   JWKSTimeout: () => (/* binding */ JWKSTimeout),\n/* harmony export */   JWSInvalid: () => (/* binding */ JWSInvalid),\n/* harmony export */   JWSSignatureVerificationFailed: () => (/* binding */ JWSSignatureVerificationFailed),\n/* harmony export */   JWTClaimValidationFailed: () => (/* binding */ JWTClaimValidationFailed),\n/* harmony export */   JWTExpired: () => (/* binding */ JWTExpired),\n/* harmony export */   JWTInvalid: () => (/* binding */ JWTInvalid)\n/* harmony export */ });\nclass JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nclass JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nclass JOSEAlgNotAllowed extends JOSEError {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nclass JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nclass JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nclass JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nclass JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nclass JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nclass JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nclass JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nclass JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nclass JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nclass JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nclass JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/util/errors.js\n");

/***/ })

};
;