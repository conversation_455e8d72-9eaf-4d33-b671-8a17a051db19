'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { FaChevronDown, FaHome, FaInfoCircle, FaHeadset, FaShoppingCart, FaSignOutAlt, FaUser, FaUserCircle, FaUpload } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { signIn, signOut, useSession } from 'next-auth/react';
import { ThemeToggle } from "./ui/theme-toggle";

export function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [activeIndicator, setActiveIndicator] = useState({ left: 0, width: 0, top: 0 });
  const [avatarFailed, setAvatarFailed] = useState(false);
  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const navItemRefs = useRef<{ [key: string]: HTMLElement | null }>({});
  const pathname = usePathname();
  const { data: session, status } = useSession();

  // Check if current page is a light header page
  const isPricingPage = pathname === '/pricing';
  const isDashboardPage = pathname === '/dashboard' || pathname.startsWith('/dashboard/');
  const isLightHeaderPage = isPricingPage || isDashboardPage;
  const isTextDark = isScrolled || isLightHeaderPage;

  // Handle avatar loading errors
  const handleAvatarError = () => {
    setAvatarFailed(true);
  };

  // Track scrolling for navbar appearance change
  useEffect(() => {
    if (window.scrollY > 20) {
      setIsScrolled(true);
    }

    const handleScroll = () => {
      if (window.scrollY > 20) {
        if (!isScrolled) setIsScrolled(true);
      } else {
        if (isScrolled) setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isScrolled]);

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  }, [pathname]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!activeDropdown) return;

      const dropdownRef = dropdownRefs.current[activeDropdown];

      if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
        const isClickOnToggle = event.target instanceof Element &&
          event.target.closest(`button[data-dropdown="${activeDropdown}"]`);

        if (!isClickOnToggle) {
          setActiveDropdown(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdown]);

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);

  // Update active indicator position
  useEffect(() => {
    if (typeof window === 'undefined') return;

    let updateTimeout: NodeJS.Timeout | null = null;
    const debouncedUpdate = () => {
      if (updateTimeout) clearTimeout(updateTimeout);
      updateTimeout = setTimeout(() => {
        updateActiveIndicator();
      }, 100);
    };

    updateActiveIndicator();

    const resizeObserver = new ResizeObserver(() => {
      debouncedUpdate();
    });

    resizeObserver.observe(document.body);

    return () => {
      if (updateTimeout) clearTimeout(updateTimeout);
      resizeObserver.disconnect();
    };
  }, [pathname, isScrolled]);

  // Determines if a link is active
  const isActive = (path: string) => {
    if (path === '/') {
      return pathname === '/';
    }
    return pathname === path;
  };

  // Get the active item key based on the current path
  const getActiveItemKey = () => {
    if (pathname === '/') {
      return '';
    }

    for (const [key, item] of Object.entries(navLinks)) {
      if (item.type === 'link' && isActive(item.path)) {
        return key;
      }
    }

    return '';
  };

  // Update the active indicator position based on the current pathname
  const updateActiveIndicator = () => {
    const activeKey = getActiveItemKey();

    if (!activeKey) {
      setActiveIndicator({ left: 0, width: 0, top: 0 });
      return;
    }

    const activeElement = navItemRefs.current[activeKey];

    if (activeElement) {
      const rect = activeElement.getBoundingClientRect();
      setActiveIndicator({
        left: rect.left,
        width: rect.width,
        top: rect.top + rect.height - 2
      });
    }
  };

  // Toggle dropdown menu
  const toggleDropdown = (name: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }

    setActiveDropdown(activeDropdown === name ? null : name);
  };

  const handleSignOut = async () => {
    try {
      setActiveDropdown(null);
      await signOut({ callbackUrl: '/' });
    } catch (error) {
      console.error('Error during sign out:', error);
      window.location.href = '/';
    }
  };

  // Structure for navbar links
  const navLinks = [
    {
      name: 'Forside',
      path: '/',
      icon: <FaHome className="mr-2" />,
      type: 'link'
    },
    {
      name: 'Priser',
      path: '/pricing',
      icon: <FaShoppingCart className="mr-2" />,
      type: 'link'
    }
  ];

  return (
    <>
      {/* Fixed navbar */}
      <nav className={`fixed w-full z-50 transition-all duration-350 ease-in-out ${
        isScrolled || isLightHeaderPage
          ? 'bg-white/95 backdrop-blur-sm text-gray-800 shadow-lg py-2 sm:py-2'
          : 'bg-transparent text-white py-3 sm:py-4'
      }`}>
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex justify-between items-center">
            {/* Logo */}
            <Link href="/" className="flex items-center group z-10">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-2 sm:mr-3">
                <span className="text-white font-bold text-lg">F</span>
              </div>
              <span className={`text-lg sm:text-xl font-bold transition-all duration-500 ease-in-out group-hover:translate-x-1 ${
                isTextDark ? 'text-gray-800' : 'text-white'
              }`}>
                FlyFiles
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1 lg:space-x-3 z-10">
              <div className="flex space-x-1 lg:space-x-3 relative">
                {/* Animated active indicator */}
                <motion.div
                  className={`absolute h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} rounded-full z-0`}
                  initial={false}
                  animate={{
                    left: activeIndicator.left,
                    width: activeIndicator.width,
                    top: activeIndicator.top,
                  }}
                  transition={{
                    type: "spring",
                    stiffness: 350,
                    damping: 30
                  }}
                />

                {Object.entries(navLinks).map(([key, item], index) => (
                  <Link
                    key={index}
                    href={item.path}
                    ref={(el) => { navItemRefs.current[key] = el; }}
                    className={`font-medium relative px-2 lg:px-3 py-2 rounded-md transition-all duration-300 ease-in-out text-sm lg:text-base ${
                      isActive(item.path)
                        ? (isTextDark ? 'text-blue-600' : 'text-white font-bold')
                        : (isTextDark ? 'text-gray-800 hover:text-blue-600 hover:bg-blue-50' : 'text-white hover:text-blue-200 hover:bg-white/10')
                    } group`}
                  >
                    <span className="flex items-center">
                      <span className="md:hidden lg:inline-block">{item.icon}</span>
                      {item.name}
                    </span>
                    <span className={`absolute bottom-0 left-0 h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} transition-all duration-300 ease-in-out ${
                      isActive(item.path) ? 'w-full' : 'w-0 group-hover:w-full'
                    }`}></span>
                  </Link>
                ))}
              </div>

              {/* User Authentication */}
              {status === "loading" ? (
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse ml-2" />
              ) : session ? (
                <>
                  {/* Dashboard Link */}
                  <Link href="/dashboard" className="ml-2">
                    <button className="group relative">
                      <div className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full blur-sm opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="relative bg-gradient-to-b from-blue-600 to-blue-500 text-white px-4 sm:px-5 py-2 rounded-full flex items-center justify-center border-none transition-all duration-300 hover:scale-105 hover:shadow-lg">
                          <div className="w-8 h-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center mr-2">
                            <FaUpload className="w-4 h-4" />
                          </div>
                          <span className="text-sm font-medium">Dashboard</span>
                        </div>
                      </div>
                    </button>
                  </Link>

                  {/* User Profile Dropdown */}
                  <div className="relative group ml-1 sm:ml-2 z-50">
                    <button
                      onClick={(e) => toggleDropdown('userMenu', e)}
                      data-dropdown="userMenu"
                      className="flex items-center space-x-2 py-1.5 px-3 rounded-full transition-all duration-300 hover:scale-105 hover:shadow-md"
                      style={{
                        background: activeDropdown === 'userMenu'
                          ? 'linear-gradient(to bottom right, #4f47e6, #3182ce)'
                          : 'transparent',
                        boxShadow: activeDropdown === 'userMenu'
                          ? '0 4px 12px rgba(66, 153, 225, 0.2)'
                          : 'none',
                      }}
                      onMouseEnter={(e) => {
                        if (activeDropdown !== 'userMenu') {
                          e.currentTarget.style.background = isTextDark
                            ? 'rgba(59, 130, 246, 0.08)'
                            : 'rgba(255, 255, 255, 0.15)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (activeDropdown !== 'userMenu') {
                          e.currentTarget.style.background = 'transparent';
                        }
                      }}
                    >
                      <div className="h-8 w-8 rounded-full overflow-hidden border-2 border-blue-400 transition-all duration-300 group-hover:border-blue-500">
                        {session.user?.image && !avatarFailed ? (
                          <Image
                            src={session.user.image}
                            alt={session.user.name || 'User'}
                            width={32}
                            height={32}
                            className="h-full w-full object-cover"
                            unoptimized
                            onError={handleAvatarError}
                          />
                        ) : (
                          <div className="h-full w-full bg-blue-400 flex items-center justify-center text-white group-hover:bg-blue-500 transition-colors duration-300">
                            <FaUser />
                          </div>
                        )}
                      </div>
                      <span className={`hidden md:block text-sm font-medium transition-all duration-300 ${
                        activeDropdown === 'userMenu'
                          ? 'text-white'
                          : isTextDark ? 'text-gray-800 group-hover:text-blue-600' : 'text-white'
                      }`}>
                        {session.user?.name || 'Bruger'}
                      </span>
                      <motion.div
                        animate={{ rotate: activeDropdown === 'userMenu' ? 180 : 0 }}
                        transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
                        className="ml-1"
                      >
                        <FaChevronDown className={`h-3 w-3 ${
                          activeDropdown === 'userMenu'
                            ? 'text-white'
                            : isTextDark ? 'text-gray-600 group-hover:text-blue-600' : 'text-white'
                        }`} />
                      </motion.div>
                    </button>

                    {/* User Dropdown Menu */}
                    <AnimatePresence>
                      {activeDropdown === 'userMenu' && (
                        <motion.div
                          initial={{ opacity: 0, y: 10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: 10, scale: 0.95 }}
                          transition={{ duration: 0.2 }}
                          className="absolute right-0 mt-2 w-52 bg-white rounded-xl shadow-lg overflow-hidden z-50 border border-blue-100"
                          ref={(el) => { dropdownRefs.current['userMenu'] = el; }}
                        >
                          <div className="py-2">
                            <div className="px-4 py-2 border-b border-gray-100">
                              <p className="text-sm font-medium text-gray-600">Logget ind som</p>
                              <p className="text-sm font-semibold text-gray-800 truncate">{session.user?.email || session.user?.name}</p>
                            </div>
                            <Link
                              href="/dashboard"
                              className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150"
                            >
                              <FaUserCircle className="mr-2 text-blue-500" />
                              Dashboard
                            </Link>
                            <div className="border-t border-gray-100 my-1"></div>
                            <button
                              onClick={handleSignOut}
                              className="flex items-center w-full text-left px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 transition-colors duration-150"
                            >
                              <FaSignOutAlt className="mr-2" />
                              Log ud
                            </button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </>
              ) : (
                <button
                  onClick={() => signIn('google')}
                  className="group relative ml-1 sm:ml-2"
                >
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full blur-sm opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative bg-gradient-to-b from-blue-600 to-blue-500 text-white px-4 sm:px-5 py-2 rounded-full flex items-center justify-center border-none transition-all duration-300 hover:scale-105 hover:shadow-lg">
                      <div className="w-8 h-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center mr-2">
                        <FaUser className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium">Log ind</span>
                    </div>
                  </div>
                </button>
              )}

              <ThemeToggle />
            </div>

            {/* Mobile Menu Button */}
            <button
              className={`md:hidden relative z-60 focus:outline-none transition-all duration-300 ease-in-out p-2 -mr-2`}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              <div className={`w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${
                isMenuOpen ? 'transform rotate-45 translate-y-2 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'
              }`}></div>
              <div className={`w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${
                isMenuOpen ? 'opacity-0 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'
              }`}></div>
              <div className={`w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${
                isMenuOpen ? 'transform -rotate-45 -translate-y-2 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'
              }`}></div>
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.25, ease: "easeInOut" }}
            className="fixed inset-0 bg-white z-40 overflow-y-auto"
          >
            <div className="container mx-auto px-4 pt-20 sm:pt-24 pb-8 h-full flex flex-col">
              <div className="flex flex-col space-y-1 sm:space-y-2 text-center text-base sm:text-lg">
                {Object.entries(navLinks).map(([key, item], index) => (
                  <Link
                    key={index}
                    href={item.path}
                    className={`py-2.5 sm:py-3 px-4 transition-all duration-300 ease-in-out ${isActive(item.path) ? 'text-blue-600 font-bold' : 'text-gray-800 hover:text-blue-600'}`}
                  >
                    <span className="relative group flex justify-center items-center">
                      {item.icon}{item.name}
                      <span className={`absolute -bottom-1 left-0 h-0.5 bg-blue-600 transition-all duration-300 ease-in-out ${isActive(item.path) ? 'w-full' : 'w-0 sm:group-hover:w-full'}`}></span>
                    </span>
                  </Link>
                ))}
              </div>

              {/* Mobile CTA */}
              <div className="mt-auto pb-4 sm:pb-8">
                {/* User profile section for mobile - Only shown when logged in */}
                {session && (
                  <div className="mb-6 border-t border-gray-100 pt-6">
                    <div className="flex items-center justify-center mb-4">
                      <div className="h-16 w-16 rounded-full overflow-hidden border-2 border-blue-400 mr-3">
                        {session.user?.image ? (
                          <Image
                            src={session.user.image}
                            alt={session.user.name || 'User'}
                            width={64}
                            height={64}
                            className="h-full w-full object-cover"
                            unoptimized
                          />
                        ) : (
                          <div className="h-full w-full bg-blue-400 flex items-center justify-center text-white">
                            <FaUser size={24} />
                          </div>
                        )}
                      </div>
                      <div className="text-left">
                        <h3 className="font-bold text-lg text-gray-800">
                          {session.user?.name || 'Bruger'}
                        </h3>
                        <p className="text-sm text-gray-500">{session.user?.email}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-3 mb-4">
                      <Link
                        href="/dashboard"
                        className="py-2 px-4 bg-gray-100 text-gray-800 rounded-lg text-center font-medium hover:bg-gray-200 transition-colors"
                      >
                        Dashboard
                      </Link>
                    </div>

                    <button
                      onClick={handleSignOut}
                      className="w-full py-3 px-4 bg-red-50 text-red-600 rounded-lg font-bold flex items-center justify-center hover:bg-red-100 transition-colors duration-300 hover:shadow-md"
                    >
                      <FaSignOutAlt className="mr-2" />
                      Log ud
                    </button>
                  </div>
                )}

                {/* Login button - Only shown when not logged in */}
                {!session && (
                  <button
                    onClick={() => signIn('google')}
                    className="block w-full py-2.5 sm:py-3 px-4 bg-blue-600 text-white text-center rounded-lg font-bold text-base sm:text-lg transition-all duration-300 ease-in-out hover:bg-blue-700 shadow-md hover:shadow-lg transform hover:scale-[1.02] mb-6 flex items-center justify-center"
                  >
                    <FaUser className="mr-2 text-lg" />
                    Log ind med Google
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}