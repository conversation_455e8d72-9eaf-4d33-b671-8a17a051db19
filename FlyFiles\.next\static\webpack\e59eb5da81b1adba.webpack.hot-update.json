{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMicka%5C%5CCascadeProjects%5C%5CFlyFiles%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/components/ui/card.tsx", "(app-pages-browser)/./src/app/page.tsx"]}