@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Arrow animation for links */
.arrow-wrapper {
  display: inline-flex;
  align-items: center;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.group:hover .arrow-wrapper {
  transform: translateX(8px);
}

.arrow-wrapper svg {
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.group:hover .arrow-wrapper svg {
  transform: scale(1.1);
}

/* 3D Cube Animation */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.cube {
  transition: transform 1s ease;
}

.scene:hover .cube-rotate {
  transform: rotateY(180deg) rotateX(45deg);
}

.cube-face {
  backface-visibility: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.cube-face-front {
  transform: translateZ(128px);
}

.cube-face-back {
  transform: rotateY(180deg) translateZ(128px);
}

.cube-face-right {
  transform: rotateY(90deg) translateZ(128px);
}

.cube-face-left {
  transform: rotateY(-90deg) translateZ(128px);
}

.cube-face-top {
  transform: rotateX(90deg) translateZ(128px);
}

.cube-face-bottom {
  transform: rotateX(-90deg) translateZ(128px);
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Server Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

@keyframes countUp {
  0% { content: "0"; }
  20% { content: "1"; }
  40% { content: "3"; }
  60% { content: "5"; }
  100% { content: "6"; }
}

.animate-[countUp_1s_ease-in-out_forwards]::after {
  content: "0";
  animation: countUp 1s ease-in-out forwards;
}

/* Calendar specific animations */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeIn {
  animation: scaleIn 0.2s ease-out forwards;
}

/* Hover animations */
.transition-transform {
  transition: transform 0.3s ease;
}

.hover\:transform:hover {
  transform: scale(1.05);
}

/* Form styles */
input, textarea, select {
  color: #333 !important;
  background-color: white !important;
}

input::placeholder, textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}

select {
  appearance: menulist !important;
  padding-right: 2rem !important;
}

select option {
  color: #333 !important;
  background-color: white !important;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

.animate-typing {
  animation: typing 8s steps(40, end) infinite;
}
